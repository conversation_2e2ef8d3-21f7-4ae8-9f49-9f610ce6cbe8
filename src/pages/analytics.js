import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ProtectedRoute from '../components/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import {
  ChartBarIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  UsersIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function Analytics() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = useState(null);
  const [insights, setInsights] = useState(null);
  const [loading, setLoading] = useState(true);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [timeframe, setTimeframe] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
    fetchInsights();
  }, [timeframe]);

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`/api/dashboard/analytics?timeframe=${timeframe}`);
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchInsights = async () => {
    try {
      setInsightsLoading(true);
      const response = await fetch(`/api/ai/insights?type=sales&timeframe=${timeframe}`);
      if (response.ok) {
        const data = await response.json();
        setInsights(data.insights);
      }
    } catch (error) {
      console.error('Error fetching AI insights:', error);
    } finally {
      setInsightsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, description, color = 'blue' }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 text-${color}-600`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <div className="flex items-center text-xs text-muted-foreground">
            {trend === 'up' ? (
              <ArrowTrendingUpIcon className="h-3 w-3 text-green-500 mr-1" />
            ) : (
              <ArrowTrendingDownIcon className="h-3 w-3 text-red-500 mr-1" />
            )}
            <span className={trend === 'up' ? 'text-green-500' : 'text-red-500'}>
              {trendValue}
            </span>
            <span className="ml-1">{description}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const InsightCard = ({ title, items, icon: Icon, color = 'blue' }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className={`h-5 w-5 text-${color}-600`} />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {items && items.length > 0 ? (
            items.map((item, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className={`w-2 h-2 rounded-full bg-${color}-500 mt-2 flex-shrink-0`}></div>
                <p className="text-sm text-gray-700">{item}</p>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500">No insights available</p>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <ProtectedRoute>
        <DashboardLayout title="Analytics">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout title="Analytics">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Analytics & Insights</h1>
              <p className="text-gray-600">AI-powered sales analytics and recommendations</p>
            </div>
            <div className="flex items-center gap-4">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              <Button onClick={fetchInsights} disabled={insightsLoading}>
                <SparklesIcon className="h-4 w-4 mr-2" />
                {insightsLoading ? 'Generating...' : 'Refresh AI Insights'}
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Revenue"
              value={formatCurrency(analytics?.sales?.totalValue)}
              icon={CurrencyDollarIcon}
              trend="up"
              trendValue="+12.5%"
              description="from last period"
              color="green"
            />
            <StatCard
              title="Active Deals"
              value={analytics?.sales?.totalDeals || 0}
              icon={ChartBarIcon}
              trend="up"
              trendValue="+8.2%"
              description="from last period"
              color="blue"
            />
            <StatCard
              title="Win Rate"
              value={`${analytics?.sales?.winRate || 0}%`}
              icon={ArrowTrendingUpIcon}
              trend="up"
              trendValue="+2.3%"
              description="from last period"
              color="purple"
            />
            <StatCard
              title="Customers"
              value={analytics?.customers?.totalCustomers || 0}
              icon={UsersIcon}
              trend="up"
              trendValue="+5.1%"
              description="from last period"
              color="orange"
            />
          </div>

          {/* AI Insights Section */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <SparklesIcon className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-bold text-gray-900">AI-Powered Insights</h2>
              <Badge variant="outline" className="bg-white">
                Powered by Google Gemini
              </Badge>
            </div>
            
            {insightsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-gray-600">Generating AI insights...</span>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <InsightCard
                  title="Key Insights"
                  items={insights?.insights}
                  icon={LightBulbIcon}
                  color="yellow"
                />
                <InsightCard
                  title="Recommendations"
                  items={insights?.recommendations}
                  icon={CheckCircleIcon}
                  color="green"
                />
                <InsightCard
                  title="Risks & Opportunities"
                  items={insights?.risks_opportunities}
                  icon={ExclamationTriangleIcon}
                  color="orange"
                />
                <InsightCard
                  title="Next Actions"
                  items={insights?.next_actions}
                  icon={ClockIcon}
                  color="blue"
                />
              </div>
            )}
          </div>

          {/* Performance Charts Placeholder */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>
                  Monthly revenue performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Chart visualization coming soon</p>
                    <p className="text-sm text-gray-400">Will be powered by AI analytics</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales Pipeline</CardTitle>
                <CardDescription>
                  Deals by stage and probability
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <ArrowTrendingUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Pipeline visualization coming soon</p>
                    <p className="text-sm text-gray-400">AI-powered pipeline analysis</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Leads</span>
                    <span className="font-semibold">{analytics?.sales?.totalDeals || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Qualified</span>
                    <span className="font-semibold">{Math.round((analytics?.sales?.totalDeals || 0) * 0.7)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Proposals</span>
                    <span className="font-semibold">{Math.round((analytics?.sales?.totalDeals || 0) * 0.4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Won</span>
                    <span className="font-semibold text-green-600">{analytics?.sales?.wonDeals || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center py-8">
                    <UsersIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">Performance data coming soon</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center py-8">
                    <ClockIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">Activity feed coming soon</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
