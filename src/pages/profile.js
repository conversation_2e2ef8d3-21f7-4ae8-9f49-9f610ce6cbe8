import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';

import { useSession } from 'next-auth/react';
import Layout from '@/components/layouts/Layout';

import ProtectedRoute from '../components/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,      // <-- Use this
  ShoppingBagIcon,
  EyeIcon,
  ArrowTrendingDownIcon     // <-- Use this
} from '@heroicons/react/24/outline';

export default function Dashboard() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if(session) {
      setLoading(false);
    };
  }, [session]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout title="Dashboard">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout title="Profile" noheader={true}>
        <Head>
          <title>Profile | TTSDiscovery</title>
        </Head>

        <div className="space-y-6 min-h-[calc(100vh-theme(spacing.20))]">
          {/* Header Section */}
          <div className="mb-6">
            <div className="flex flex-row gap-1">
              <div className="flex flex-col gap-1">
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                  Profile
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Manage your workspaces and their organizations
                </p>
              </div>

            </div>
          </div>
        </div>
        <div className="space-y-6 p-2">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {session?.user?.name}!
            </h1>
            <p className="text-blue-100">
              Here's what's happening with your sales today.
            </p>
          </div>

          {/* Performance Chart Placeholder */}
          <Card>
            <CardHeader>
              <CardTitle>Session</CardTitle>
              <CardDescription>
                data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  {/* <p className="text-gray-500"><code>{JSON.stringify(session, null, 2)}</code></p> */}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
