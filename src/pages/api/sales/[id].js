import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { SaleModel } from '../../../lib/models/Sale';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;
  const { id } = req.query;

  if (!ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid sale ID' });
  }

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session, id);
        break;
      case 'PUT':
        await handlePut(req, res, session, id);
        break;
      case 'DELETE':
        await handleDelete(req, res, session, id);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Sale API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session, id) {
  const sale = await SaleModel.findById(id);

  if (!sale) {
    return res.status(404).json({ message: 'Sale not found' });
  }

  // Check permissions
  if (session.user.role !== 'admin' && 
      sale.assignedTo.toString() !== session.user.id &&
      sale.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  res.status(200).json(sale);
}

async function handlePut(req, res, session, id) {
  const sale = await SaleModel.findById(id);

  if (!sale) {
    return res.status(404).json({ message: 'Sale not found' });
  }

  // Check permissions
  if (session.user.role !== 'admin' && 
      sale.assignedTo.toString() !== session.user.id &&
      sale.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  const updateData = { ...req.body };
  
  // Convert string IDs to ObjectIds
  if (updateData.customerId) {
    updateData.customerId = new ObjectId(updateData.customerId);
  }
  if (updateData.assignedTo) {
    updateData.assignedTo = new ObjectId(updateData.assignedTo);
  }
  if (updateData.productIds) {
    updateData.productIds = updateData.productIds.map(id => new ObjectId(id));
  }
  if (updateData.expectedCloseDate) {
    updateData.expectedCloseDate = new Date(updateData.expectedCloseDate);
  }

  // Remove fields that shouldn't be updated directly
  delete updateData._id;
  delete updateData.createdAt;
  delete updateData.createdBy;

  const result = await SaleModel.update(id, updateData);

  if (result.matchedCount === 0) {
    return res.status(404).json({ message: 'Sale not found' });
  }

  const updatedSale = await SaleModel.findById(id);
  
  res.status(200).json({
    message: 'Sale updated successfully',
    sale: updatedSale
  });
}

async function handleDelete(req, res, session, id) {
  const sale = await SaleModel.findById(id);

  if (!sale) {
    return res.status(404).json({ message: 'Sale not found' });
  }

  // Check permissions - only admin or creator can delete
  if (session.user.role !== 'admin' && 
      sale.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  const result = await SaleModel.delete(id);

  if (result.deletedCount === 0) {
    return res.status(404).json({ message: 'Sale not found' });
  }

  res.status(200).json({ message: 'Sale deleted successfully' });
}
