import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter";
import clientPromise from "../../../lib/mongodb";
import {
  verifyPassword,
  findUserByEmail,
  createUser,
  checkInvitation,
  markInvitationUsed
} from "../../../lib/firebase/utils";

export const authOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Find user in database
        const user = await findUserByEmail(credentials.email);

        if (!user) {
          return null;
        }

        // Verify password
        const isValid = await verifyPassword(credentials.password, user.password);

        if (!isValid) {
          return null;
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
          image: user.image,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account.provider === "google") {
        // Check if user exists
        const existingUser = await findUserByEmail(user.email);

        if (!existingUser) {
          // Check invitation for new Google users
          const invitation = await checkInvitation(user.email);

          if (!invitation) {
            return false; // Reject sign-in if no invitation
          }

          // Create new user
          await createUser({
            email: user.email,
            name: user.name,
            image: user.image,
            role: invitation.role || 'user',
            provider: 'google',
          });

          // Mark invitation as used
          await markInvitationUsed(user.email);
        }
      }

      return true;
    },
    async jwt({ token, user, account }) {
      if (user) {
        // First time JWT is created
        const dbUser = await findUserByEmail(user.email);
        token.role = dbUser?.role || 'user';
        token.id = dbUser?._id?.toString() || user.id;
      }
      return token;
    },
    async session({ session, token }) {
      // Add role and id to session
      session.user.role = token.role;
      session.user.id = token.id;
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions); 