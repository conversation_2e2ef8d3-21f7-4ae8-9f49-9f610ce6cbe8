import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { InvitationModel } from '../../../lib/models/Invitation';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Only admins can manage invitations
  if (session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session);
        break;
      case 'POST':
        await handlePost(req, res, session);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Invitations API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session) {
  const { 
    page = 1, 
    limit = 10, 
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build filter
  const filter = {};
  
  if (status) {
    filter.status = status;
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    sort
  };

  const result = await InvitationModel.findAll(filter, options);
  
  res.status(200).json(result);
}

async function handlePost(req, res, session) {
  const {
    email,
    role = 'user',
    message,
    expiresInDays = 7
  } = req.body;

  // Validation
  if (!email) {
    return res.status(400).json({ 
      message: 'Email is required' 
    });
  }

  if (!['admin', 'user'].includes(role)) {
    return res.status(400).json({ 
      message: 'Invalid role. Must be admin or user' 
    });
  }

  // Check if there's already a pending invitation for this email
  const existingInvitation = await InvitationModel.findByEmail(email);
  if (existingInvitation) {
    return res.status(400).json({ 
      message: 'There is already a pending invitation for this email' 
    });
  }

  // Set expiration date
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + parseInt(expiresInDays));

  const invitationData = {
    email,
    role,
    message,
    invitedBy: new ObjectId(session.user.id),
    expiresAt
  };

  const invitation = await InvitationModel.create(invitationData);
  
  // TODO: Send invitation email here
  // await sendInvitationEmail(invitation);
  
  res.status(201).json({
    message: 'Invitation created successfully',
    invitation: {
      ...invitation,
      // Don't expose the full code in response for security
      code: invitation.code.substring(0, 4) + '****'
    }
  });
}
