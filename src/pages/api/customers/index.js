import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { CustomerModel } from '../../../lib/models/Customer';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session);
        break;
      case 'POST':
        await handlePost(req, res, session);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Customers API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session) {
  const { 
    page = 1, 
    limit = 10, 
    status, 
    assignedTo, 
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build filter
  const filter = {};
  
  if (status) {
    filter.status = status;
  }
  
  if (assignedTo) {
    filter.assignedTo = new ObjectId(assignedTo);
  }

  // Non-admin users can only see their own customers
  if (session.user.role !== 'admin') {
    filter.assignedTo = new ObjectId(session.user.id);
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    sort,
    search
  };

  const result = await CustomerModel.findAll(filter, options);
  
  res.status(200).json(result);
}

async function handlePost(req, res, session) {
  const {
    name,
    email,
    phone,
    company,
    position,
    industry,
    website,
    address,
    socialMedia,
    tags = [],
    source,
    status = 'prospect',
    priority = 'medium',
    assignedTo,
    customFields = {}
  } = req.body;

  // Validation
  if (!name || !email) {
    return res.status(400).json({ 
      message: 'Name and email are required' 
    });
  }

  // Check if customer already exists
  const existingCustomer = await CustomerModel.findByEmail(email);
  if (existingCustomer) {
    return res.status(400).json({ 
      message: 'Customer with this email already exists' 
    });
  }

  const customerData = {
    name,
    email,
    phone,
    company,
    position,
    industry,
    website,
    address: address || {},
    socialMedia: socialMedia || {},
    tags,
    source,
    status,
    priority,
    assignedTo: assignedTo ? new ObjectId(assignedTo) : new ObjectId(session.user.id),
    createdBy: new ObjectId(session.user.id),
    customFields
  };

  const customer = await CustomerModel.create(customerData);
  
  res.status(201).json({
    message: 'Customer created successfully',
    customer
  });
}
