import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripHorizontal } from 'lucide-react';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Bell, Mail, Menu } from 'lucide-react';

const AdminDashboard = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState(30);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);

  const MIN_LEFT_PANEL_WIDTH_PX = 300;
  const MIN_RIGHT_PANEL_WIDTH_PX = 500;

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  const handleMouseDown = () => setIsResizing(true);

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      let newWidthPx = e.clientX - containerRect.left;
      const maxLeftPanelWidthPx = containerRect.width - MIN_RIGHT_PANEL_WIDTH_PX;
      newWidthPx = Math.max(MIN_LEFT_PANEL_WIDTH_PX, Math.min(newWidthPx, maxLeftPanelWidthPx));
      const newWidthPercent = (newWidthPx / containerRect.width) * 100;
      setLeftPanelWidth(newWidthPercent);
    }
  };

  const handleMouseUp = () => setIsResizing(false);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const TopRightIcons = () => ( 
    <div className="flex items-center space-x-4">
      <Button variant="outline">New Report</Button>
      <Button variant="default">Add User</Button>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="ghost" size="icon">
        <Mail className="h-5 w-5" />
      </Button>
      <Avatar className="h-8 w-8">
        {/* Placeholder for user avatar */}
        <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
      </Avatar>
    </div>
  )

  return (
    <ProtectedRoute>
    <AdminLayout isDrawerOpen={isDrawerOpen} 
      toggleDrawer={toggleDrawer} 
      title={'Hotels'} 
      breadCrumbs={['Home', 'Hotels']}
      // topRightIcons={<TopRightIcons />} 
      >
      <main ref={containerRef} className="flex flex-1 overflow-hidden">
        {/* Left Panel */}
        <div style={{ width: `${leftPanelWidth}%` }} className="flex flex-col flex-shrink-0 p-6 overflow-y-auto bg-white border-r border-gray-200">
          <h3 className="text-xl font-semibold mb-4">Static Left Panel</h3>
          <p>This panel is static and will not scroll vertically.</p>
          <p>It has a minimum width of {MIN_LEFT_PANEL_WIDTH_PX}px.</p>
          <div className="mt-4 space-y-2">
            <Card>
              <CardHeader><CardTitle>Left Card 1</CardTitle></CardHeader>
              <CardContent><p>Content for left card 1.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 2</CardTitle></CardHeader>
              <CardContent><p>Content for left card 2.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 3</CardTitle></CardHeader>
              <CardContent><p>Content for left card 3.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 4</CardTitle></CardHeader>
              <CardContent><p>Content for left card 4.</p></CardContent>
            </Card>
          </div>
        </div>

        {/* Resizer Bar */}
        <div
          className="w-1 bg-gray-300 cursor-ew-resize flex items-center justify-center"
          onMouseDown={handleMouseDown}
        >
          <GripHorizontal className="h-[50px] w-5 text-gray-800" />
        </div>

        {/* Right Panel */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Total Users</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">1,234</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Active Sessions</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">567</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">$89,012</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                  <li>User &apos;John Doe&apos; logged in.</li>
                  <li>New report &apos;Sales Q1&apos; generated.</li>
                  <li>Settings updated by &apos;Admin&apos;.</li>
                  <li>User &apos;Jane Smith&apos; registered.</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </AdminLayout>
    </ProtectedRoute>
  );
};

export default AdminDashboard;