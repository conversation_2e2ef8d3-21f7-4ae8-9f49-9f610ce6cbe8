import React, { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripHorizontal, Send, MessageCircle, Bot, User } from 'lucide-react';
import ProtectedRoute from '@/components/ProtectedRoute';
import Layout from '@/components/layouts/Layout';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bell, Mail, Menu } from 'lucide-react';

const AdminDashboard = () => {
  const { data: session } = useSession();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState(30);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Merhaba! Size nasıl yardımcı olabilirim?",
      sender: "bot",
      timestamp: new Date().toLocaleTimeString()
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const chatEndRef = useRef(null);

  const MIN_LEFT_PANEL_WIDTH_PX = 300;
  const MIN_RIGHT_PANEL_WIDTH_PX = 500;

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  // Chat functions
  const sendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        text: newMessage,
        sender: "user",
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages(prev => [...prev, userMessage]);
      setNewMessage("");

      // Simulate bot response
      setTimeout(() => {
        const botMessage = {
          id: messages.length + 2,
          text: "Mesajınızı aldım. Size yardımcı olmaya çalışıyorum...",
          sender: "bot",
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, botMessage]);
      }, 1000);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Scroll to bottom when new message is added
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleMouseDown = () => setIsResizing(true);

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      let newWidthPx = e.clientX - containerRect.left;
      const maxLeftPanelWidthPx = containerRect.width - MIN_RIGHT_PANEL_WIDTH_PX;
      newWidthPx = Math.max(MIN_LEFT_PANEL_WIDTH_PX, Math.min(newWidthPx, maxLeftPanelWidthPx));
      const newWidthPercent = (newWidthPx / containerRect.width) * 100;
      setLeftPanelWidth(newWidthPercent);
    }
  };

  const handleMouseUp = () => setIsResizing(false);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const TopRightIcons = () => ( 
    <div className="flex items-center space-x-4">
      <Button variant="outline">New Report</Button>
      <Button variant="default">Add User</Button>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="ghost" size="icon">
        <Mail className="h-5 w-5" />
      </Button>
      <Avatar className="h-8 w-8">
        {/* Placeholder for user avatar */}
        <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
      </Avatar>
    </div>
  )

  return (
    <ProtectedRoute>
      <Layout isDrawerOpen={isDrawerOpen}
        toggleDrawer={toggleDrawer}
        title={'Hotels - Domestic'}
        breadCrumbs={['Home', 'Hotels - Domestic']}
      // topRightIcons={<TopRightIcons />} 
      >

        <div className="space-y-1">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              Domestic Hotels!
            </h1>
            <p className="text-blue-100">
              Here's what's happening with your sales today.
            </p>
          </div>

          <main ref={containerRef} className="flex flex-1">
            {/* Left Panel - Chat Area - Sticky */}
            <div style={{ width: `${leftPanelWidth}%` }} className="flex flex-col flex-shrink-0 bg-white border-r border-gray-200 h-[calc(100vh-120px)] sticky top-4">
              {/* Chat Header */}
              <Card className="m-4 mb-0 rounded-b-none border-b-0">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                    AI Chat Assistant
                  </CardTitle>
                </CardHeader>
              </Card>

              {/* Chat Messages Area */}
              <div className="flex-1 mx-4 mb-0 overflow-hidden">
                <Card className="h-full rounded-t-none rounded-b-none border-t-0 border-b-0">
                  <CardContent className="p-0 h-full">
                    <ScrollArea className="h-full p-4">
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`flex items-start gap-2 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                                message.sender === 'user'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-200 text-gray-600'
                              }`}>
                                {message.sender === 'user' ? (
                                  <User className="h-4 w-4" />
                                ) : (
                                  <Bot className="h-4 w-4" />
                                )}
                              </div>
                              <div className={`rounded-lg p-3 ${
                                message.sender === 'user'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                <p className="text-sm">{message.text}</p>
                                <p className={`text-xs mt-1 ${
                                  message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                                }`}>
                                  {message.timestamp}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div ref={chatEndRef} />
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>

              {/* Chat Input Area */}
              <Card className="m-4 mt-0 rounded-t-none border-t-0 mb-16">
                <CardContent className="p-4">
                  <div className="flex gap-2">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Mesajınızı yazın..."
                      className="flex-1"
                    />
                    <Button onClick={sendMessage} size="icon" className="flex-shrink-0">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Resizer Bar */}
            <div
              className="w-1 bg-gray-300 hover:bg-blue-500 cursor-ew-resize flex items-center justify-center"
              onMouseDown={handleMouseDown}
            >
              <GripHorizontal className="h-[50px] w-5 text-gray-800" />
            </div>

            {/* Right Panel - Scrollable */}
            <div className="flex-1 p-6">
              <div className="mb-4 space-y-6">
                {/* Additional Cards for Testing Scroll */}
                <Card>
                  <CardHeader>
                    <CardTitle>Hotel Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">150</p>
                        <p className="text-sm text-gray-500">Total Hotels</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">89%</p>
                        <p className="text-sm text-gray-500">Occupancy Rate</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Revenue Growth</span>
                        <span className="font-semibold text-green-600">+12.5%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Customer Satisfaction</span>
                        <span className="font-semibold text-blue-600">4.8/5</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Booking Conversion</span>
                        <span className="font-semibold text-purple-600">23.4%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Top Performing Hotels</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">Grand Palace Hotel</p>
                          <p className="text-sm text-gray-500">Istanbul</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">₺2,450,000</p>
                          <p className="text-sm text-green-600">+15.2%</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">Seaside Resort</p>
                          <p className="text-sm text-gray-500">Antalya</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">₺1,890,000</p>
                          <p className="text-sm text-green-600">****%</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">Mountain View Lodge</p>
                          <p className="text-sm text-gray-500">Cappadocia</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">₺1,650,000</p>
                          <p className="text-sm text-green-600">+11.3%</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Monthly Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <p className="text-gray-500">Chart placeholder</p>
                        <p className="text-sm text-gray-400">Revenue trends over time</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Total Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">1,234</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Active Sessions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">567</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">$89,012</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>

        </div>
    </Layout>
    </ProtectedRoute>
  );
};

export default AdminDashboard;