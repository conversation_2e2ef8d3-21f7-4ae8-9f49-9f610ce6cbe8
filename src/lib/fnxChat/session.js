import fs from 'fs/promises';
import path from 'path';

// In-memory cache for chat sessions
const chatSessions = new Map();
const SESSIONS_DIR = path.join(process.cwd(), 'chat-sessions');

async function ensureSessionsDir() {
  try {
    await fs.mkdir(SESSIONS_DIR, { recursive: true });
  } catch (error) {
    console.error('Error creating chat-sessions directory:', error);
  }
}

ensureSessionsDir();

async function saveSession(sessionId, session) {
  try {
    const filePath = path.join(SESSIONS_DIR, `${sessionId}.json`);
    await fs.writeFile(filePath, JSON.stringify(session, null, 2));
    chatSessions.set(sessionId, session);
  } catch (error) {
    console.error(`Failed to save session ${sessionId}:`, error);
  }
}

async function getOrCreateChatSession(sessionId) {
  // 1. Try to get from in-memory cache
  if (chatSessions.has(sessionId)) {
    return chatSessions.get(sessionId);
  }

  // 2. Try to load from file system
  try {
    const filePath = path.join(SESSIONS_DIR, `${sessionId}.json`);
    const data = await fs.readFile(filePath, 'utf-8');
    const session = JSON.parse(data);
    chatSessions.set(sessionId, session);
    return session;
  } catch (error) {
    // If file doesn't exist, it's not an error, we'll create a new session.
    if (error.code !== 'ENOENT') {
      console.error(`Failed to read session ${sessionId}:`, error);
    }
  }

  // 3. Create a new session
  const newSession = {
    sessionId,
    createdAt: new Date().toISOString(),
    history: [
        { role: 'user', parts: [{ text: "Sen bir seyahat asistanısın. Adın Juli. Kullanıcılara seyahatleri hakkında yardımcı ol."}] },
        { role: 'model', parts: [{ text: "Merhaba! Ben Juli, kişisel seyahat asistanınız. Tur arama, rezervasyon yapma veya seyahatinizi planlama konusunda size nasıl yardımcı olabilirim?" }] }
    ],
    context: {
      // User preferences, selected tours, etc. will go here
    },
  };
  await saveSession(sessionId, newSession);
  return newSession;
}

export const sessionManager = {
  get: getOrCreateChatSession,
  save: saveSession,
}; 