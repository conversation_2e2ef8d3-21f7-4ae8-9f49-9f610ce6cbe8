// Comprehensive API middleware for authentication, authorization, and security
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../pages/api/auth/[...nextauth]';

// NextAuth authentication middleware
export const authenticateUser = async (req, res, next) => {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Attach user data to request
    req.user = session.user;
    req.session = session;

    if (next) next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Authentication failed' });
  }
};

// Authorization middleware for admin-only routes
export const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  if (next) next();
};

// Authorization middleware for onboarded users only
export const requireOnboarding = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (!req.user.onboardingCompleted) {
    return res.status(403).json({ error: 'User onboarding required' });
  }

  if (next) next();
};

// Enhanced rate limiting middleware
const rateLimitStore = new Map();

export const rateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'Too many requests, please try again later.',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator = null,
  } = options;

  return (req, res, next) => {
    const key = keyGenerator ? keyGenerator(req) : getClientIdentifier(req);
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    if (rateLimitStore.has(key)) {
      const requests = rateLimitStore.get(key).filter(time => time > windowStart);
      rateLimitStore.set(key, requests);
    }

    const requests = rateLimitStore.get(key) || [];

    if (requests.length >= max) {
      res.status(429).json({
        error: 'Rate limit exceeded',
        message,
        retryAfter: Math.ceil((requests[0] + windowMs - now) / 1000)
      });
      return;
    }

    // Add current request
    requests.push(now);
    rateLimitStore.set(key, requests);

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', max);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, max - requests.length));
    res.setHeader('X-RateLimit-Reset', new Date(now + windowMs).toISOString());

    if (next) next();
  };
};

// Get client identifier for rate limiting
function getClientIdentifier(req) {
  // Try to get real IP from various headers
  const forwarded = req.headers['x-forwarded-for'];
  const realIp = req.headers['x-real-ip'];
  const ip = forwarded ? forwarded.split(',')[0] : realIp || req.connection?.remoteAddress;

  return req.user?.id || ip || 'unknown';
}

// Enhanced input validation and sanitization middleware
export const validateInput = (schema) => {
  return (req, res, next) => {
    try {
      // Basic sanitization
      if (req.body) {
        req.body = sanitizeObject(req.body);
      }
      if (req.query) {
        req.query = sanitizeObject(req.query);
      }

      // Validate against schema if provided
      if (schema && req.body) {
        const validation = validateSchema(req.body, schema);
        if (!validation.valid) {
          res.status(400).json({
            error: 'Validation failed',
            details: validation.errors
          });
          return;
        }
      }

      if (next) next();
    } catch (error) {
      res.status(400).json({
        error: 'Invalid input',
        message: 'Request contains invalid data'
      });
    }
  };
};

// Sanitize object recursively
function sanitizeObject(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return sanitizeValue(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    const sanitizedKey = sanitizeValue(key);
    sanitized[sanitizedKey] = sanitizeObject(value);
  }
  return sanitized;
}

// Sanitize individual values
function sanitizeValue(value) {
  if (typeof value !== 'string') {
    return value;
  }

  // Remove potentially dangerous characters
  return value
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

// Basic schema validation
function validateSchema(data, schema) {
  const errors = [];

  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];

    if (rules.required && (value === undefined || value === null || value === '')) {
      errors.push(`${field} is required`);
      continue;
    }

    if (value !== undefined && value !== null) {
      if (rules.type && typeof value !== rules.type) {
        errors.push(`${field} must be of type ${rules.type}`);
      }

      if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
        errors.push(`${field} must be at least ${rules.minLength} characters long`);
      }

      if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
        errors.push(`${field} must be no more than ${rules.maxLength} characters long`);
      }

      if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
        errors.push(`${field} format is invalid`);
      }

      if (rules.enum && !rules.enum.includes(value)) {
        errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Enhanced CORS middleware
export const corsMiddleware = (req, res, next) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    process.env.NEXT_PUBLIC_APP_URL,
    'http://localhost:3000',
    'https://localhost:3000'
  ].filter(Boolean);

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (next) next();
};

// Enhanced security headers middleware
export const securityHeaders = (req, res, next) => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://generativelanguage.googleapis.com",
    "frame-ancestors 'none'",
  ].join('; ');

  res.setHeader('Content-Security-Policy', csp);

  // Additional security headers
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('X-DNS-Prefetch-Control', 'off');

  if (next) next();
};

// Logging middleware
export const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms - User: ${req.user?.email || 'Anonymous'}`);
  });

  next();
};

// Enhanced error handling middleware
export const errorHandler = (error, req, res) => {
  console.error('API Error:', {
    error: error.message,
    stack: error.stack,
    url: req?.url,
    method: req?.method,
    user: req?.user?.email,
    timestamp: new Date().toISOString()
  });

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation error',
      details: isDevelopment ? error.message : 'Invalid input data'
    });
  }

  if (error.message?.includes('auth') || error.message?.includes('token')) {
    return res.status(401).json({
      error: 'Authentication error',
      details: 'Please refresh your authentication'
    });
  }

  if (error.message?.includes('permission') || error.message?.includes('access')) {
    return res.status(403).json({
      error: 'Access denied',
      details: 'Insufficient permissions'
    });
  }

  // Generic error response
  return res.status(500).json({
    error: 'Internal server error',
    details: isDevelopment ? error.message : 'An unexpected error occurred'
  });
};

// Compose multiple middleware functions
export const composeMiddleware = (...middlewares) => {
  return (req, res, next) => {
    const runMiddleware = (index) => {
      if (index >= middlewares.length) {
        return next();
      }

      const middleware = middlewares[index];
      middleware(req, res, (error) => {
        if (error) {
          return next(error);
        }
        runMiddleware(index + 1);
      });
    };

    runMiddleware(0);
  };
};

// Enhanced helper function to create protected API routes
export const createProtectedRoute = (handler, options = {}) => {
  const {
    requireAdmin: needsAdmin = false,
    requireOnboarding: needsOnboarding = false,
    rateLimit: rateLimitOptions = { max: 100, windowMs: 15 * 60 * 1000 },
    validation: validationSchema = null,
    allowedMethods = ['GET', 'POST', 'PUT', 'DELETE']
  } = options;

  return async (req, res) => {
    try {
      // Apply security headers first
      securityHeaders(req, res);

      // Apply CORS
      corsMiddleware(req, res);

      // Check allowed methods
      if (!allowedMethods.includes(req.method)) {
        res.setHeader('Allow', allowedMethods);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
      }

      // Apply rate limiting
      if (rateLimitOptions) {
        await new Promise((resolve, reject) => {
          rateLimit(rateLimitOptions)(req, res, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      // Apply authentication
      await new Promise((resolve, reject) => {
        authenticateUser(req, res, (error) => {
          if (error) reject(error);
          else resolve();
        });
      });

      // Apply authorization checks
      if (needsAdmin) {
        await new Promise((resolve, reject) => {
          requireAdmin(req, res, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      if (needsOnboarding) {
        await new Promise((resolve, reject) => {
          requireOnboarding(req, res, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      // Apply input validation
      if (validationSchema) {
        await new Promise((resolve, reject) => {
          validateInput(validationSchema)(req, res, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      // Log request
      requestLogger(req, res);

      // Execute the actual handler
      await handler(req, res);
    } catch (error) {
      errorHandler(error, req, res);
    }
  };
};

// Utility function for API route method handling
export const withMethods = (handlers) => {
  return createProtectedRoute(async (req, res) => {
    const handler = handlers[req.method];
    if (!handler) {
      res.setHeader('Allow', Object.keys(handlers));
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
    return await handler(req, res);
  }, {
    allowedMethods: Object.keys(handlers)
  });
};
