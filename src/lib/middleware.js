// API middleware for authentication and authorization
import { adminAuth, adminDb } from './firebaseAdmin';

// Authentication middleware
export const authenticateUser = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split('Bearer ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'No authentication token provided' });
    }

    // Verify the Firebase ID token
    const decodedToken = await adminAuth.verifyIdToken(token);
    
    // Get user data from Firestore
    const userDoc = await adminDb.collection('users').doc(decodedToken.uid).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Attach user data to request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      ...userDoc.data()
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Invalid authentication token' });
  }
};

// Authorization middleware for admin-only routes
export const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  next();
};

// Authorization middleware for onboarded users only
export const requireOnboarding = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (!req.user.isOnboarded) {
    return res.status(403).json({ error: 'User onboarding required' });
  }

  next();
};

// Rate limiting middleware (simple in-memory implementation)
const rateLimitStore = new Map();

export const rateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return (req, res, next) => {
    const key = req.user?.uid || req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    if (rateLimitStore.has(key)) {
      const requests = rateLimitStore.get(key).filter(time => time > windowStart);
      rateLimitStore.set(key, requests);
    }

    // Check current request count
    const currentRequests = rateLimitStore.get(key) || [];
    
    if (currentRequests.length >= maxRequests) {
      return res.status(429).json({ 
        error: 'Too many requests',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // Add current request
    currentRequests.push(now);
    rateLimitStore.set(key, currentRequests);

    next();
  };
};

// Input validation middleware
export const validateInput = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        error: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }

    next();
  };
};

// CORS middleware
export const corsMiddleware = (req, res, next) => {
  const allowedOrigins = [
    'http://localhost:3000',
    'https://your-domain.com' // Add your production domain
  ];

  const origin = req.headers.origin;
  
  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
};

// Security headers middleware
export const securityHeaders = (req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  next();
};

// Logging middleware
export const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms - User: ${req.user?.email || 'Anonymous'}`);
  });

  next();
};

// Error handling middleware
export const errorHandler = (error, req, res, next) => {
  console.error('API Error:', error);

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation error',
      details: isDevelopment ? error.message : 'Invalid input data'
    });
  }

  if (error.code === 'auth/id-token-expired') {
    return res.status(401).json({
      error: 'Token expired',
      details: 'Please refresh your authentication token'
    });
  }

  // Generic error response
  return res.status(500).json({
    error: 'Internal server error',
    details: isDevelopment ? error.message : 'An unexpected error occurred'
  });
};

// Compose multiple middleware functions
export const composeMiddleware = (...middlewares) => {
  return (req, res, next) => {
    const runMiddleware = (index) => {
      if (index >= middlewares.length) {
        return next();
      }

      const middleware = middlewares[index];
      middleware(req, res, (error) => {
        if (error) {
          return next(error);
        }
        runMiddleware(index + 1);
      });
    };

    runMiddleware(0);
  };
};

// Helper function to create protected API routes
export const createProtectedRoute = (handler, options = {}) => {
  const {
    requireAdmin: needsAdmin = false,
    requireOnboarding: needsOnboarding = true,
    rateLimit: rateLimitOptions = null
  } = options;

  const middlewares = [
    corsMiddleware,
    securityHeaders,
    requestLogger,
    authenticateUser
  ];

  if (needsOnboarding) {
    middlewares.push(requireOnboarding);
  }

  if (needsAdmin) {
    middlewares.push(requireAdmin);
  }

  if (rateLimitOptions) {
    middlewares.push(rateLimit(rateLimitOptions.maxRequests, rateLimitOptions.windowMs));
  }

  return async (req, res) => {
    try {
      await new Promise((resolve, reject) => {
        composeMiddleware(...middlewares)(req, res, (error) => {
          if (error) reject(error);
          else resolve();
        });
      });

      await handler(req, res);
    } catch (error) {
      errorHandler(error, req, res, () => {});
    }
  };
};
