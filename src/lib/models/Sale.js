// Sales model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';

export const SaleSchema = {
  _id: ObjectId,
  title: String,
  description: String,
  customerId: ObjectId, // reference to Customer
  productIds: [ObjectId], // references to Products
  assignedTo: ObjectId, // reference to User
  createdBy: ObjectId, // reference to User
  stage: String, // 'lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost'
  value: Number, // monetary value
  currency: String, // 'USD', 'EUR', etc.
  probability: Number, // 0-100
  expectedCloseDate: Date,
  actualCloseDate: Date,
  source: String, // 'website', 'referral', 'cold-call', 'email', 'social-media'
  priority: String, // 'low', 'medium', 'high', 'urgent'
  status: String, // 'active', 'inactive', 'archived'
  tags: [String],
  notes: [
    {
      _id: ObjectId,
      content: String,
      createdBy: ObjectId,
      createdAt: Date,
      isPrivate: Boolean,
    }
  ],
  activities: [
    {
      _id: ObjectId,
      type: String, // 'call', 'email', 'meeting', 'task', 'note'
      description: String,
      createdBy: ObjectId,
      createdAt: Date,
      scheduledAt: Date,
      completedAt: Date,
      status: String, // 'pending', 'completed', 'cancelled'
    }
  ],
  attachments: [
    {
      _id: ObjectId,
      filename: String,
      url: String,
      size: Number,
      type: String,
      uploadedBy: ObjectId,
      uploadedAt: Date,
    }
  ],
  createdAt: Date,
  updatedAt: Date,
};

export class SaleModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db();
    return await db.collection('sales').findOne({ _id: new ObjectId(id) });
  }

  static async create(saleData) {
    const client = await clientPromise;
    const db = client.db();
    
    const sale = {
      ...saleData,
      stage: saleData.stage || 'lead',
      status: saleData.status || 'active',
      probability: saleData.probability || 0,
      currency: saleData.currency || 'USD',
      priority: saleData.priority || 'medium',
      tags: saleData.tags || [],
      notes: [],
      activities: [],
      attachments: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection('sales').insertOne(sale);
    return { ...sale, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db();
    
    const result = await db.collection('sales').updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db();
    
    const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;
    
    // Build aggregation pipeline for populated data
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'assignedTo',
          foreignField: '_id',
          as: 'assignedUser'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creator'
        }
      },
      { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$assignedUser', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$creator', preserveNullAndEmptyArrays: true } },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const sales = await db.collection('sales').aggregate(pipeline).toArray();
    const total = await db.collection('sales').countDocuments(filter);
    
    return {
      sales,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async addNote(saleId, noteData) {
    const client = await clientPromise;
    const db = client.db();
    
    const note = {
      _id: new ObjectId(),
      ...noteData,
      createdAt: new Date(),
    };
    
    return await db.collection('sales').updateOne(
      { _id: new ObjectId(saleId) },
      { 
        $push: { notes: note },
        $set: { updatedAt: new Date() }
      }
    );
  }

  static async addActivity(saleId, activityData) {
    const client = await clientPromise;
    const db = client.db();
    
    const activity = {
      _id: new ObjectId(),
      ...activityData,
      createdAt: new Date(),
      status: activityData.status || 'pending',
    };
    
    return await db.collection('sales').updateOne(
      { _id: new ObjectId(saleId) },
      { 
        $push: { activities: activity },
        $set: { updatedAt: new Date() }
      }
    );
  }

  static async updateStage(saleId, stage, probability) {
    const client = await clientPromise;
    const db = client.db();
    
    const updateData = {
      stage,
      updatedAt: new Date(),
    };
    
    if (probability !== undefined) {
      updateData.probability = probability;
    }
    
    if (stage === 'closed-won' || stage === 'closed-lost') {
      updateData.actualCloseDate = new Date();
      updateData.status = 'inactive';
    }
    
    return await db.collection('sales').updateOne(
      { _id: new ObjectId(saleId) },
      { $set: updateData }
    );
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db();
    
    return await db.collection('sales').deleteOne({ _id: new ObjectId(id) });
  }

  static async getAnalytics(filter = {}) {
    const client = await clientPromise;
    const db = client.db();
    
    const pipeline = [
      { $match: filter },
      {
        $group: {
          _id: null,
          totalValue: { $sum: '$value' },
          totalDeals: { $sum: 1 },
          avgDealSize: { $avg: '$value' },
          wonDeals: {
            $sum: { $cond: [{ $eq: ['$stage', 'closed-won'] }, 1, 0] }
          },
          lostDeals: {
            $sum: { $cond: [{ $eq: ['$stage', 'closed-lost'] }, 1, 0] }
          },
          wonValue: {
            $sum: { $cond: [{ $eq: ['$stage', 'closed-won'] }, '$value', 0] }
          },
        }
      }
    ];
    
    const result = await db.collection('sales').aggregate(pipeline).toArray();
    return result[0] || {};
  }
}
