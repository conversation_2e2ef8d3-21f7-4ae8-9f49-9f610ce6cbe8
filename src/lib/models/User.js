// User model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';

export const UserSchema = {
  _id: ObjectId,
  name: String,
  email: String, // unique
  password: String, // hashed, optional for OAuth users
  image: String, // profile image URL
  role: String, // 'admin', 'user'
  provider: String, // 'google', 'credentials'
  isActive: <PERSON>olean,
  onboardingCompleted: Boolean,
  preferences: {
    theme: String, // 'light', 'dark', 'system'
    notifications: {
      email: <PERSON><PERSON><PERSON>,
      push: <PERSON>olean,
      sales: Boolean,
      reports: Boolean,
    },
    dashboard: {
      layout: String,
      widgets: Array,
    },
  },
  profile: {
    phone: String,
    department: String,
    position: String,
    bio: String,
    timezone: String,
  },
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date,
};

export class UserModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db();
    return await db.collection('users').findOne({ _id: new ObjectId(id) });
  }

  static async findByEmail(email) {
    const client = await clientPromise;
    const db = client.db();
    return await db.collection('users').findOne({ email });
  }

  static async create(userData) {
    const client = await clientPromise;
    const db = client.db();
    
    const user = {
      ...userData,
      isActive: true,
      onboardingCompleted: false,
      preferences: {
        theme: 'system',
        notifications: {
          email: true,
          push: true,
          sales: true,
          reports: true,
        },
        dashboard: {
          layout: 'default',
          widgets: [],
        },
      },
      profile: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection('users').insertOne(user);
    return { ...user, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db();
    
    const result = await db.collection('users').updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async updateLastLogin(id) {
    const client = await clientPromise;
    const db = client.db();
    
    await db.collection('users').updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          lastLoginAt: new Date(),
          updatedAt: new Date()
        } 
      }
    );
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db();
    
    const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;
    
    const users = await db.collection('users')
      .find(filter, { projection: { password: 0 } })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray();
    
    const total = await db.collection('users').countDocuments(filter);
    
    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db();
    
    return await db.collection('users').deleteOne({ _id: new ObjectId(id) });
  }
}
