
import { ChevronLeft, ChevronRight, LayoutDashboard, Users, Settings, Bar<PERSON>hart } from 'lucide-react';
import {
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ShoppingBagIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

export const menuItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
    href: '/dashboard',
    section: 'main',
    workspace: ['all'],
    roles: ['all'],
    disabled: false,
    weight: 10
  },

  {
    id: 'sales',
    label: 'Sales',
    icon: ChartBarIcon,
    href: '/sales',
    section: 'ms',
    sectionTitle: 'Marketing & Sales',
    weight: 20,
    workspace: ['ms'],
    roles: ['all'],
    disabled: true,
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: UsersIcon,
    href: '/customers',
    section: 'ms',
    sectionTitle: 'Marketing & Sales',
    weight: 10,
    workspace: ['ms'],
    roles: ['all'],
    disabled: true,
  },
  {
    id: 'products',
    label: 'Products',
    icon: ShoppingBagIcon,
    href: '/products',
    section: 'products',
    sectionTitle: 'Products',
    weight: 10,
    workspace: ['ms'],
    roles: ['all'],
    disabled: true,
  },
  {
    id: 'hotels',
    label: 'Hotels-Domestic',
    icon: CogIcon,
    href: '/hotels-domestic',
    section: 'products',
    sectionTitle: 'Products',
    weight: 10,
    workspace: ['ms'],
    roles: ['all'],
    disabled: false,
  },
  {
    id: 'hotels',
    label: 'Hotels-International',
    icon: CogIcon,
    href: '/hotels',
    section: 'products',
    sectionTitle: 'Products',
    weight: 20,
    workspace: ['ms'],
    roles: ['all'],
    disabled: false,
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: BarChart,
    href: '/analytics',
    section: 'others',
    sectionTitle: 'Others',
    weight: 20,
    workspace: ['ms'],
    roles: ['all'],
    disabled: false,
  },

  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
    section: 'others',
    sectionTitle: 'Others',
    weight: 10,
    workspace: ['all'],
    roles: ['all'],
    disabled: true,
  },
  {
    id: 'admin',
    label: 'Admin Panel',
    icon: BuildingOfficeIcon,
    href: '/admin',
    section: 'admin',
    sectionTitle: 'Admin',
    weight: 10,
    workspace: ['all'],
    roles: ['admin'],
    disabled: false,
  },
  {
    id: 'adminUsers',
    label: 'Users',
    icon: UsersIcon,
    href: '/admin/users',
    section: 'admin',
    sectionTitle: 'Admin',
    weight: 20,
    roles: ['admin'],
    disabled: false,
  },
  {
    id: 'invitations',
    label: 'Invitations',
    icon: UserCircleIcon,
    href: '/admin/invitations',
    section: 'admin',
    sectionTitle: 'Admin',
    weight: 30,
    roles: ['admin'],
    disabled: false,
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: UserCircleIcon,
    href: '/profile',
    section: 'others',
    sectionTitle: 'Others',
    weight: 30,
    roles: ['all'],
    disabled: false,
  },
];
