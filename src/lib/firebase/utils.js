import bcrypt from 'bcrypt';
import clientPromise from '../mongodb';

export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

export async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

export async function findUserByEmail(email) {
  const client = await clientPromise;
  const db = client.db();
  
  return await db.collection('users').findOne({ email });
}

export async function createUser(userData) {
  const client = await clientPromise;
  const db = client.db();
  
  const user = {
    ...userData,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: userData.role || 'user',
    isActive: true,
    onboardingCompleted: false,
  };
  
  const result = await db.collection('users').insertOne(user);
  return { ...user, _id: result.insertedId };
}

export async function checkInvitation(email) {
  const client = await clientPromise;
  const db = client.db();
  
  const invitation = await db.collection('invitations').findOne({
    email,
    status: 'pending',
    expiresAt: { $gt: new Date() }
  });
  
  return invitation;
}

export async function markInvitationUsed(email) {
  const client = await clientPromise;
  const db = client.db();
  
  await db.collection('invitations').updateOne(
    { email, status: 'pending' },
    { 
      $set: { 
        status: 'used',
        usedAt: new Date()
      }
    }
  );
}

export async function updateUserRole(userId, role) {
  const client = await clientPromise;
  const db = client.db();
  
  await db.collection('users').updateOne(
    { _id: userId },
    { 
      $set: { 
        role,
        updatedAt: new Date()
      }
    }
  );
}
