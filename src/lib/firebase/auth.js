// Authentication helper functions
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signOut,
  onAuthStateChanged,
  updateProfile
} from 'firebase/auth';
import { auth, googleProvider } from './firebase';
import { createUser, getUser, getInvitation, useInvitation } from './dbModels';

// Sign in with email and password
export const signInWithEmail = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { user: userCredential.user, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};

// Sign up with email and password (requires invitation)
export const signUpWithEmail = async (email, password, displayName, invitationCode) => {
  try {
    // First, verify the invitation
    const invitation = await getInvitation(invitationCode);
    if (!invitation) {
      return { user: null, error: 'Invalid or expired invitation code' };
    }

    // Create the user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Update the user's display name
    await updateProfile(user, { displayName });

    // Create user document in Firestore
    await createUser(user.uid, {
      email: user.email,
      displayName: displayName,
      authProvider: 'email',
      invitationId: invitation.id
    });

    // Mark invitation as used
    await useInvitation(invitation.id, user.uid);

    return { user, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};

// Sign in with Google (requires invitation for new users)
export const signInWithGoogle = async (invitationCode = null) => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;

    // Check if user already exists in our database
    const existingUser = await getUser(user.uid);
    
    if (!existingUser) {
      // New user - requires invitation
      if (!invitationCode) {
        // Sign out the user since they don't have an invitation
        await signOut(auth);
        return { user: null, error: 'Invitation code required for new users' };
      }

      const invitation = await getInvitation(invitationCode);
      if (!invitation) {
        await signOut(auth);
        return { user: null, error: 'Invalid or expired invitation code' };
      }

      // Create user document in Firestore
      await createUser(user.uid, {
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        authProvider: 'google',
        invitationId: invitation.id
      });

      // Mark invitation as used
      await useInvitation(invitation.id, user.uid);
    }

    return { user, error: null };
  } catch (error) {
    return { user: null, error: error.message };
  }
};

// Sign out
export const signOutUser = async () => {
  try {
    await signOut(auth);
    return { error: null };
  } catch (error) {
    return { error: error.message };
  }
};

// Auth state observer
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, callback);
};

// Get current user with database info
export const getCurrentUserWithData = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  const userData = await getUser(user.uid);
  return {
    ...user,
    ...userData
  };
};
