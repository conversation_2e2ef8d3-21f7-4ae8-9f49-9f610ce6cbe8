import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import ProtectedRoute from '../../components/ProtectedRoute';
import DashboardLayout from '../../components/DashboardLayout';
import { auth } from '../../lib/firebase';
import {
  UserIcon,
  ShieldCheckIcon,
  BanIcon,
  CheckCircleIcon
} from '@heroicons/react/outline';

export default function UsersAdmin() {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchUsers();
    }
  }, [user]);

  const fetchUsers = async () => {
    try {
      const token = await auth.currentUser.getIdToken();
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        setError('Failed to fetch users');
      }
    } catch (error) {
      setError('Error fetching users');
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId, newRole) => {
    if (!confirm(`Are you sure you want to change this user's role to ${newRole}?`)) return;

    try {
      const token = await auth.currentUser.getIdToken();
      const response = await fetch(`/api/admin/users?userId=${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ role: newRole })
      });

      if (response.ok) {
        setSuccess('User role updated successfully');
        fetchUsers();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update user role');
      }
    } catch (error) {
      setError('Error updating user role');
    }
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';
    
    if (!confirm(`Are you sure you want to ${action} this user?`)) return;

    try {
      const token = await auth.currentUser.getIdToken();
      
      if (newStatus) {
        // Activate user
        const response = await fetch(`/api/admin/users?userId=${userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ isActive: true })
        });

        if (response.ok) {
          setSuccess('User activated successfully');
          fetchUsers();
        } else {
          setError('Failed to activate user');
        }
      } else {
        // Deactivate user
        const response = await fetch(`/api/admin/users?userId=${userId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          setSuccess('User deactivated successfully');
          fetchUsers();
        } else {
          setError('Failed to deactivate user');
        }
      }
    } catch (error) {
      setError(`Error ${action}ing user`);
    }
  };

  const getRoleIcon = (role) => {
    return role === 'admin' ? ShieldCheckIcon : UserIcon;
  };

  const getRoleColor = (role) => {
    return role === 'admin' ? 'purple' : 'blue';
  };

  if (user && user.role !== 'admin') {
    return (
      <ProtectedRoute requireOnboarding={true}>
        <DashboardLayout title="Access Denied">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600">You need admin privileges to access this page.</p>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireOnboarding={true}>
      <DashboardLayout title="User Management">
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
            <p className="text-gray-600 mt-1">
              Manage user accounts, roles, and permissions
            </p>
          </div>

          {/* Alerts */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              {success}
            </div>
          )}

          {/* Users Table */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">All Users</h3>
            </div>
            
            {loading ? (
              <div className="p-8 text-center">Loading...</div>
            ) : users.length === 0 ? (
              <div className="p-8 text-center text-gray-500">No users found</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Onboarded
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Joined
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {users.map((userData) => {
                      const RoleIcon = getRoleIcon(userData.role);
                      const isCurrentUser = userData.id === user.uid;
                      
                      return (
                        <tr key={userData.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <img
                                  className="h-10 w-10 rounded-full"
                                  src={userData.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.displayName || 'User')}&background=6366f1&color=fff`}
                                  alt=""
                                />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {userData.displayName}
                                  {isCurrentUser && <span className="ml-2 text-xs text-gray-500">(You)</span>}
                                </div>
                                <div className="text-sm text-gray-500">{userData.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getRoleColor(userData.role)}-100 text-${getRoleColor(userData.role)}-800`}>
                              <RoleIcon className="w-4 h-4 mr-1" />
                              {userData.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              userData.isActive !== false 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {userData.isActive !== false ? (
                                <>
                                  <CheckCircleIcon className="w-4 h-4 mr-1" />
                                  Active
                                </>
                              ) : (
                                <>
                                  <BanIcon className="w-4 h-4 mr-1" />
                                  Inactive
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {userData.isOnboarded ? 'Yes' : 'No'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {userData.createdAt ? new Date(userData.createdAt).toLocaleDateString() : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            {!isCurrentUser && (
                              <>
                                <button
                                  onClick={() => updateUserRole(userData.id, userData.role === 'admin' ? 'user' : 'admin')}
                                  className={`text-${userData.role === 'admin' ? 'blue' : 'purple'}-600 hover:text-${userData.role === 'admin' ? 'blue' : 'purple'}-900`}
                                >
                                  {userData.role === 'admin' ? 'Make User' : 'Make Admin'}
                                </button>
                                <button
                                  onClick={() => toggleUserStatus(userData.id, userData.isActive !== false)}
                                  className={`text-${userData.isActive !== false ? 'red' : 'green'}-600 hover:text-${userData.isActive !== false ? 'red' : 'green'}-900`}
                                >
                                  {userData.isActive !== false ? 'Deactivate' : 'Activate'}
                                </button>
                              </>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
