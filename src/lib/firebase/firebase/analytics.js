import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import ProtectedRoute from '../components/ProtectedRoute';
import DashboardLayout from '../components/DashboardLayout';
import {
  ChartBarIcon,
  RefreshIcon,
  DownloadIcon
} from '@heroicons/react/outline';

export default function Analytics() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [aiInsights, setAiInsights] = useState(null);
  const [salesData, setSalesData] = useState([]);

  useEffect(() => {
    // Simulate loading sales data
    setSalesData([
      { month: 'Jan', revenue: 45000, deals: 12, growth: 8.2 },
      { month: 'Feb', revenue: 52000, deals: 15, growth: 15.6 },
      { month: 'Mar', revenue: 48000, deals: 13, growth: -7.7 },
      { month: 'Apr', revenue: 61000, deals: 18, growth: 27.1 },
      { month: 'May', revenue: 58000, deals: 16, growth: -4.9 },
      { month: 'Jun', revenue: 67000, deals: 20, growth: 15.5 },
    ]);
  }, []);

  const generateAIInsights = async (analysisType = 'performance') => {
    setLoading(true);
    setError('');

    try {
      const token = await auth.currentUser.getIdToken();
      const response = await fetch('/api/ai-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          analysisType,
          timeRange: 'quarter',
          includeTeamData: user?.role === 'admin'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAiInsights({
          summary: data.insights.summary || "AI analysis completed successfully",
          recommendations: data.insights.recommendations || data.insights.insights || [],
          keyMetrics: {
            dataPoints: data.dataPoints,
            analysisType: data.analysisType,
            timeRange: data.timeRange,
            generatedAt: data.generatedAt
          },
          rawInsights: data.insights
        });
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to generate AI insights');

        // Fallback to demo data
        setAiInsights({
          summary: "Demo: Your sales performance shows strong growth patterns. The team is performing well with consistent deal closure rates.",
          recommendations: [
            "Focus on strategies that led to recent growth",
            "Investigate any performance dips to prevent similar patterns",
            "Consider expanding successful approaches"
          ],
          keyMetrics: {
            dataPoints: salesData.length,
            analysisType: 'demo',
            timeRange: 'quarter',
            generatedAt: new Date().toISOString()
          }
        });
      }
    } catch (error) {
      console.error('Error generating AI insights:', error);
      setError('Error generating AI insights');
    } finally {
      setLoading(false);
    }
  };

  const SimpleChart = ({ data, title, dataKey, color = "indigo" }) => (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={item.month} className="flex items-center">
            <div className="w-12 text-sm text-gray-600">{item.month}</div>
            <div className="flex-1 mx-4">
              <div className="bg-gray-200 rounded-full h-4">
                <div
                  className={`bg-${color}-600 h-4 rounded-full transition-all duration-500`}
                  style={{
                    width: `${(item[dataKey] / Math.max(...data.map(d => d[dataKey]))) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="w-20 text-sm text-gray-900 text-right">
              {dataKey === 'revenue' ? `$${item[dataKey].toLocaleString()}` : item[dataKey]}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const AIInsightsPanel = () => (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">AI-Powered Insights</h3>
        <div className="flex space-x-2">
          <select
            className="text-sm border border-gray-300 rounded-md px-2 py-1"
            onChange={(e) => generateAIInsights(e.target.value)}
            disabled={loading}
          >
            <option value="">Select Analysis Type</option>
            <option value="performance">Performance Analysis</option>
            <option value="forecasting">Sales Forecasting</option>
            <option value="optimization">Process Optimization</option>
          </select>
          <button
            onClick={() => generateAIInsights('performance')}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? (
              <RefreshIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
            ) : (
              <ChartBarIcon className="-ml-1 mr-2 h-4 w-4" />
            )}
            {loading ? 'Analyzing...' : 'Analyze'}
          </button>
        </div>
      </div>

      {aiInsights ? (
        <div className="space-y-6">
          {/* Summary */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Summary</h4>
            <p className="text-sm text-blue-800">{aiInsights.summary}</p>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{aiInsights.keyMetrics.dataPoints}</div>
              <div className="text-sm text-gray-600">Data Points Analyzed</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{aiInsights.keyMetrics.analysisType}</div>
              <div className="text-sm text-gray-600">Analysis Type</div>
            </div>
          </div>

          {/* Analysis Metadata */}
          <div className="text-xs text-gray-500 bg-gray-50 rounded p-2">
            Generated: {new Date(aiInsights.keyMetrics.generatedAt).toLocaleString()} |
            Time Range: {aiInsights.keyMetrics.timeRange}
          </div>

          {/* Recommendations */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">AI Recommendations</h4>
            <ul className="space-y-2">
              {aiInsights.recommendations.map((rec, index) => (
                <li key={index} className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-indigo-600 rounded-full mt-2"></div>
                  <p className="ml-3 text-sm text-gray-700">{rec}</p>
                </li>
              ))}
            </ul>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h4 className="mt-2 text-sm font-medium text-gray-900">No insights generated yet</h4>
          <p className="mt-1 text-sm text-gray-500">Click the button above to generate AI-powered insights from your sales data.</p>
        </div>
      )}
    </div>
  );

  return (
    <ProtectedRoute requireOnboarding={true}>
      <DashboardLayout title="Sales Analytics">
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Sales Analytics</h2>
                <p className="text-gray-600 mt-1">
                  AI-powered insights and data visualization for your sales performance
                </p>
              </div>
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <DownloadIcon className="-ml-1 mr-2 h-4 w-4" />
                Export Report
              </button>
            </div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SimpleChart
              data={salesData}
              title="Monthly Revenue"
              dataKey="revenue"
              color="green"
            />
            <SimpleChart
              data={salesData}
              title="Deals Closed"
              dataKey="deals"
              color="blue"
            />
          </div>

          {/* Growth Chart */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Growth Rate Trend</h3>
            <div className="space-y-3">
              {salesData.map((item, index) => (
                <div key={item.month} className="flex items-center">
                  <div className="w-12 text-sm text-gray-600">{item.month}</div>
                  <div className="flex-1 mx-4">
                    <div className="bg-gray-200 rounded-full h-4 relative">
                      <div className="absolute left-1/2 w-0.5 h-4 bg-gray-400"></div>
                      <div
                        className={`h-4 rounded-full transition-all duration-500 ${
                          item.growth >= 0 ? 'bg-green-600' : 'bg-red-600'
                        }`}
                        style={{
                          width: `${Math.abs(item.growth) * 2}%`,
                          marginLeft: item.growth >= 0 ? '50%' : `${50 - Math.abs(item.growth) * 2}%`
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className={`w-20 text-sm text-right ${
                    item.growth >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.growth > 0 ? '+' : ''}{item.growth}%
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* AI Insights */}
          <AIInsightsPanel />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
