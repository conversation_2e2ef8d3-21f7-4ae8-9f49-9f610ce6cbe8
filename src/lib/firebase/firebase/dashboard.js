import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import ProtectedRoute from '../components/ProtectedRoute';
import DashboardLayout from '../components/DashboardLayout';
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  CurrencyDollarIcon
} from '@heroicons/react/outline';

export default function Dashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalRevenue: 0,
    monthlyGrowth: 0,
    activeDeals: 0,
    teamMembers: 0
  });

  useEffect(() => {
    // Simulate loading dashboard data
    setTimeout(() => {
      setStats({
        totalRevenue: 125000,
        monthlyGrowth: 12.5,
        activeDeals: 24,
        teamMembers: 8
      });
    }, 1000);
  }, []);

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'indigo' }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className={`h-6 w-6 text-${color}-600`} />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {trend && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                    trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {trend === 'up' ? (
                      <TrendingUpIcon className="self-center flex-shrink-0 h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingDownIcon className="self-center flex-shrink-0 h-4 w-4 text-red-500" />
                    )}
                    <span className="ml-1">{trendValue}%</span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  const QuickActions = () => (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-4 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <ChartBarIcon className="mx-auto h-8 w-8 text-gray-400" />
            <span className="mt-2 block text-sm font-medium text-gray-900">Generate Report</span>
          </button>
          <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-4 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <UsersIcon className="mx-auto h-8 w-8 text-gray-400" />
            <span className="mt-2 block text-sm font-medium text-gray-900">Add Team Member</span>
          </button>
        </div>
      </div>
    </div>
  );

  const RecentActivity = () => (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="flow-root">
          <ul className="-mb-8">
            {[
              { id: 1, content: 'New deal closed', user: 'John Doe', time: '2 hours ago', type: 'success' },
              { id: 2, content: 'Meeting scheduled', user: 'Jane Smith', time: '4 hours ago', type: 'info' },
              { id: 3, content: 'Report generated', user: 'Mike Johnson', time: '6 hours ago', type: 'info' },
              { id: 4, content: 'Target achieved', user: 'Sarah Wilson', time: '1 day ago', type: 'success' },
            ].map((item, itemIdx) => (
              <li key={item.id}>
                <div className="relative pb-8">
                  {itemIdx !== 3 && (
                    <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                  )}
                  <div className="relative flex space-x-3">
                    <div>
                      <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                        item.type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                      }`}>
                        <ChartBarIcon className="h-4 w-4 text-white" />
                      </span>
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm text-gray-500">
                          {item.content} by <span className="font-medium text-gray-900">{item.user}</span>
                        </p>
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500">
                        {item.time}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );

  return (
    <ProtectedRoute requireOnboarding={true}>
      <DashboardLayout title="Dashboard">
        <div className="space-y-6">
          {/* Welcome Message */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.displayName}!
            </h2>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your sales team today.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Revenue"
              value={`$${stats.totalRevenue.toLocaleString()}`}
              icon={CurrencyDollarIcon}
              trend="up"
              trendValue={stats.monthlyGrowth}
              color="green"
            />
            <StatCard
              title="Monthly Growth"
              value={`${stats.monthlyGrowth}%`}
              icon={TrendingUpIcon}
              trend="up"
              trendValue="2.1"
              color="blue"
            />
            <StatCard
              title="Active Deals"
              value={stats.activeDeals}
              icon={ChartBarIcon}
              trend="up"
              trendValue="5.4"
              color="purple"
            />
            <StatCard
              title="Team Members"
              value={stats.teamMembers}
              icon={UsersIcon}
              color="indigo"
            />
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <QuickActions />
            <RecentActivity />
          </div>

          {/* AI Insights Placeholder */}
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-white mb-2">AI-Powered Insights</h3>
              <p className="text-purple-100 mb-4">
                Get intelligent recommendations and data visualizations powered by AI.
              </p>
              <button className="bg-white text-purple-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
                View Analytics
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
