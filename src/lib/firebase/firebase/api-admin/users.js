import { adminDb } from '../../../lib/firebaseAdmin';
import { createProtectedRoute } from '../../../lib/middleware';

async function handler(req, res) {
  if (req.method === 'GET') {
    // Get all users
    try {
      const usersSnapshot = await adminDb
        .collection('users')
        .orderBy('createdAt', 'desc')
        .get();

      const users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate().toISOString(),
        updatedAt: doc.data().updatedAt?.toDate().toISOString()
      }));

      return res.status(200).json(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }

  } else if (req.method === 'PUT') {
    // Update user role or status
    const { userId } = req.query;
    const { role, isActive } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    try {
      const updateData = {};
      
      if (role !== undefined) {
        if (!['user', 'admin'].includes(role)) {
          return res.status(400).json({ error: 'Invalid role. Must be "user" or "admin"' });
        }
        updateData.role = role;
      }

      if (isActive !== undefined) {
        updateData.isActive = isActive;
      }

      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({ error: 'No valid update fields provided' });
      }

      updateData.updatedAt = new Date();

      await adminDb.collection('users').doc(userId).update(updateData);

      return res.status(200).json({ message: 'User updated successfully' });
    } catch (error) {
      console.error('Error updating user:', error);
      return res.status(500).json({ error: 'Failed to update user' });
    }

  } else if (req.method === 'DELETE') {
    // Deactivate user (soft delete)
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Prevent admin from deleting themselves
    if (userId === req.user.uid) {
      return res.status(400).json({ error: 'Cannot deactivate your own account' });
    }

    try {
      await adminDb.collection('users').doc(userId).update({
        isActive: false,
        deactivatedAt: new Date(),
        deactivatedBy: req.user.uid,
        updatedAt: new Date()
      });

      return res.status(200).json({ message: 'User deactivated successfully' });
    } catch (error) {
      console.error('Error deactivating user:', error);
      return res.status(500).json({ error: 'Failed to deactivate user' });
    }

  } else {
    res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
    return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}

// Export the protected route with admin requirement
export default createProtectedRoute(handler, {
  requireAdmin: true,
  requireOnboarding: true,
  rateLimit: { maxRequests: 30, windowMs: 15 * 60 * 1000 } // 30 requests per 15 minutes
});
