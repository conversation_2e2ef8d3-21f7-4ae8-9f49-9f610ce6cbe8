import { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChange, getCurrentUserWithData, signOutUser } from '../lib/auth';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChange(async (firebaseUser) => {
      console.log('[AuthContext] onAuthStateChange fired. firebaseUser:', firebaseUser);
      if (firebaseUser) {
        // Get user data from Firestore
        const userData = await getCurrentUserWithData();
        console.log('[AuthContext] getCurrentUserWithData:', userData);
        setUser(userData);
      } else {
        setUser(null);
      }
      setLoading(false);
      console.log('[AuthContext] loading set to false');
    });

    return () => unsubscribe();
  }, []);

  const logout = async () => {
    await signOutUser();
    setUser(null);
  };

  const value = {
    user,
    loading,
    logout
  };

  console.log('[AuthContext] Render', { user, loading });
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
