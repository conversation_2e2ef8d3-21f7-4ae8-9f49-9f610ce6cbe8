// Firebase client configuration
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const xfirebaseConfig = {
  projectId: "tourai-d62b3",
  authDomain: "tourai-d62b3.firebaseapp.com",
  storageBucket: "tourai-d62b3.appspot.com",
  messagingSenderId: "100720676925805544086",
  appId: "1:131854872916:web:fd7860fd4ac2c56be6d8c1",
  xappId: "1:100720676925805544086:web:your-app-id"
};

const firebaseConfig = {
  apiKey: "AIzaSyBml_kp9ZbNn14NU2y-axNfkzmUKCDmLqM",
  authDomain: "tourai-d62b3.firebaseapp.com",
  projectId: "tourai-d62b3",
  storageBucket: "tourai-d62b3.firebasestorage.app",
  messagingSenderId: "131854872916",
  appId: "1:131854872916:web:fd7860fd4ac2c56be6d8c1",
  measurementId: "G-XTG7TXPWRK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Google Auth Provider
export const googleProvider = new GoogleAuthProvider();

export default app;
