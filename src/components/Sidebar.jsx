import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useSession, signOut } from 'next-auth/react';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { ScrollArea } from './ui/scroll-area';
import { ChevronLeft, ChevronRight, LayoutDashboard, Users, Settings, BarChart } from 'lucide-react';
import { menuItems } from '@/lib/menuitems';
import {
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ShoppingBagIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
// Dummy menu data

const Sidebar = ({ isDrawerOpen, toggleDrawer }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showTitle, setShowTitle] = useState(true);

  const { data: session } = useSession();
  const router = useRouter();
  useEffect(() => {
    if (!isSidebarCollapsed) {
      const timer = setTimeout(() => {
        setShowTitle(true);
      }, 150);
      return () => clearTimeout(timer);
    } else {
      setShowTitle(false);
    }
  }, [isSidebarCollapsed]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };
  return (
    <>
      {/* Overlay for Drawer */}
      {isDrawerOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleDrawer}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          'flex flex-col bg-gray-50 transition-all duration-300 ease-in-out h-screen',
          // Desktop: always visible, no positioning classes needed (parent handles sticky)
          'hidden md:flex',
          isSidebarCollapsed ? 'w-20' : 'w-64',
          // Mobile: drawer with fixed positioning
          'md:relative fixed inset-y-0 left-0 z-50',
          isDrawerOpen ? 'translate-x-0' : '-translate-x-full',
          'md:translate-x-0'
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1
            className={cn(
              'text-xl font-semibold text-gray-800 transition-opacity duration-150',
              showTitle ? 'opacity-100' : 'opacity-0',
              isSidebarCollapsed ? 'hidden' : ''
            )}
          >
            TTS Panel
          </h1>
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className={cn(isSidebarCollapsed ? 'ml-auto' : '', 'hidden md:flex')}>
            {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-1 px-2">
            {menuItems.map((item) => (
              <Link key={item.id} href={item.href} passHref>
                <Button
                  variant="ghost"
                  className={cn(
                    'w-full cursor-pointer',
                    !isSidebarCollapsed && 'hover:shadow-md', // Apply shadow only when not collapsed
                    isSidebarCollapsed ? 'justify-center px-0' : 'justify-start px-4'
                  )}
                  title={isSidebarCollapsed ? item.label : ''} // Show tooltip only when collapsed
                >
                  <span className={cn('h-8 w-8 flex items-center justify-center border border-gray-200 rounded-md', isSidebarCollapsed ? '' : 'mr-3')}>
                    <item.icon className={cn('h-5 w-5 text-blue-500', isSidebarCollapsed && 'hover:text-blue-800')} /> {/* Darken icon on hover when collapsed */}
                  </span>
                  <span
                    className={cn(
                      'transition-opacity duration-300',
                      !isSidebarCollapsed && 'hover:text-blue-800', // Apply text hover only when not collapsed
                      isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                    )}
                  >
                    {item.label}
                  </span>
                </Button>
              </Link>
            ))}
          </nav>
        </div>

        <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
          <div className="flex items-center w-full">
            <div className="flex-shrink-0">
              <img
                className="h-12 w-12 rounded-full"
                src={session?.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session?.user?.name || 'User')}&background=6366f1&color=fff`}
                alt=""
              />
            </div>
            <div
            className={cn(
              'flex items-center w-full transition-opacity duration-150',
              showTitle ? 'opacity-100' : 'opacity-0',
              isSidebarCollapsed ? 'hidden' : ''
            )}>
              <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-700">{session?.user?.name}</p>
              <p className="text-xs text-gray-500">{session?.user?.role}</p>
            </div>
            <button
              onClick={handleLogout}
              className="ml-3 flex-shrink-0 p-1 text-gray-400 hover:text-gray-500"
              title="Sign out"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5" />
            </button>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;