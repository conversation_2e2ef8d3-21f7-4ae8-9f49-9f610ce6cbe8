import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useSession, signOut } from 'next-auth/react';
import { cn } from '../lib/utils';
import { Button } from './ui/button';
import { ScrollArea } from './ui/scroll-area';
import { ChevronLeft, ChevronRight, LayoutDashboard, Users, Settings, BarChart } from 'lucide-react';

import {
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ShoppingBagIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
// Dummy menu data
const menuItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
    href: '/dashboard',
  },

  {
    id: 'sales',
    label: 'Sales',
    icon: ChartBarIcon,
    href: '/sales',
  },
  {
    id: 'customers',
    label: 'Customers',
    icon: UsersIcon,
    href: '/customers',
  },
  {
    id: 'products',
    label: 'Products',
    icon: ShoppingBagIcon,
    href: '/products',
  },
  {
    id: 'hotels',
    label: 'Hotels',
    icon: CogIcon,
    href: '/hotels',
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: BarChart,
    href: '/Analytics',
  },

  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
  },
  {
    id: 'admin',
    label: 'Admin Panel',
    icon: BuildingOfficeIcon,
    href: '/admin',
  },
  {
    id: 'adminUsers',
    label: 'Users',
    icon: UsersIcon,
    href: '/admin/users',
  },
  {
    id: 'invitations',
    label: 'Invitations',
    icon: UserCircleIcon,
    href: '/admin/invitations',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: UserCircleIcon,
    href: '/profile',
  },
];

const Sidebar = ({ isDrawerOpen, toggleDrawer }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showTitle, setShowTitle] = useState(true);

  const { data: session } = useSession();
  const router = useRouter();
  useEffect(() => {
    if (!isSidebarCollapsed) {
      const timer = setTimeout(() => {
        setShowTitle(true);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setShowTitle(false);
    }
  }, [isSidebarCollapsed]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };
  return (
    <>
      {/* Overlay for Drawer */}
      {isDrawerOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleDrawer}
        ></div>
      )}

      {/* Sticky Sidebar */}
      <aside
        className={cn(
          'flex flex-col bg-gray-50 transition-all duration-300 ease-in-out overflow-hidden',
          'md:flex', // Always visible on md and larger
          isSidebarCollapsed ? 'w-20' : 'w-64',
          'sticky top-0 h-screen',
          // Drawer specific styles
          'fixed inset-y-0 left-0 z-50', // Fixed position for drawer
          isDrawerOpen ? 'translate-x-0' : '-translate-x-full', // Slide in/out animation
          'md:relative md:translate-x-0' // Reset for md and larger screens
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1
            className={cn(
              'text-xl font-semibold text-gray-800 transition-opacity duration-150',
              showTitle ? 'opacity-100' : 'opacity-0',
              isSidebarCollapsed ? 'hidden' : ''
            )}
          >
            TTS Panel
          </h1>
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className={cn(isSidebarCollapsed ? 'ml-auto' : '', 'hidden md:flex')}>
            {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        </div>
        <ScrollArea className="flex-1 py-4">
          <nav className="space-y-1">
            {menuItems.map((item) => (
              <Link key={item.id} href={item.href} passHref>
                <Button
                  variant="ghost"
                  className={cn(
                    'w-full cursor-pointer mr-4',
                    !isSidebarCollapsed && 'hover:shadow-md', // Apply shadow only when not collapsed
                    isSidebarCollapsed ? 'justify-center px-0' : 'justify-start px-4'
                  )}
                  title={isSidebarCollapsed ? item.label : ''} // Show tooltip only when collapsed
                >
                  <span className={cn('h-8 w-8 flex items-center justify-center border border-gray-200 rounded-md', isSidebarCollapsed ? '' : 'mr-3')}>
                    <item.icon className={cn('h-5 w-5 text-blue-500', isSidebarCollapsed && 'hover:text-blue-800')} /> {/* Darken icon on hover when collapsed */}
                  </span>
                  <span
                    className={cn(
                      'transition-opacity duration-300',
                      !isSidebarCollapsed && 'hover:text-blue-800', // Apply text hover only when not collapsed
                      isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                    )}
                  >
                    {item.label}
                  </span>
                </Button>
              </Link>
            ))}
          </nav>
        </ScrollArea>

            <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
              <div className="flex items-center w-full">
                <div className="flex-shrink-0">
                  <img
                    className="h-8 w-8 rounded-full"
                    src={session?.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session?.user?.name || 'User')}&background=6366f1&color=fff`}
                    alt=""
                  />
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-700">{session?.user?.name}</p>
                  <p className="text-xs text-gray-500">{session?.user?.role}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="ml-3 flex-shrink-0 p-1 text-gray-400 hover:text-gray-500"
                  title="Sign out"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
      </aside>
    </>
  );
};

export default Sidebar;