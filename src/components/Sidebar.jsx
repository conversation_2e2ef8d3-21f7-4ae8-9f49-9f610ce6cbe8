import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { cn } from '../lib/utils';
import { Button } from './ui/button';
import { ScrollArea } from './ui/scroll-area';
import { ChevronLeft, ChevronRight, LayoutDashboard, Users, Settings, BarChart } from 'lucide-react';

// Dummy menu data
const menuItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
    href: '/cx',
  },
  {
    id: 'users',
    label: 'Users',
    icon: Users,
    href: '/cx/users',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/cx/settings',
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: BarChart,
    href: '/cx/reports',
  },
];

const Sidebar = ({ isDrawerOpen, toggleDrawer }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showTitle, setShowTitle] = useState(true);

  useEffect(() => {
    if (!isSidebarCollapsed) {
      const timer = setTimeout(() => {
        setShowTitle(true);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setShowTitle(false);
    }
  }, [isSidebarCollapsed]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <>
      {/* Overlay for Drawer */}
      {isDrawerOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleDrawer}
        ></div>
      )}

      {/* Sticky Sidebar */}
      <aside
        className={cn(
          'flex flex-col bg-gray-100 border-r border-gray-200 transition-all duration-300 ease-in-out overflow-hidden',
          'md:flex', // Always visible on md and larger
          isSidebarCollapsed ? 'w-20' : 'w-64',
          'sticky top-0 h-screen',
          // Drawer specific styles
          'fixed inset-y-0 left-0 z-50', // Fixed position for drawer
          isDrawerOpen ? 'translate-x-0' : '-translate-x-full', // Slide in/out animation
          'md:relative md:translate-x-0' // Reset for md and larger screens
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1
            className={cn(
              'text-xl font-semibold text-gray-800 transition-opacity duration-150',
              showTitle ? 'opacity-100' : 'opacity-0',
              isSidebarCollapsed ? 'hidden' : ''
            )}
          >
            Admin Panel
          </h1>
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className={cn(isSidebarCollapsed ? 'ml-auto' : '', 'hidden md:flex')}>
            {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        </div>
        <ScrollArea className="flex-1 py-4">
          <nav className="space-y-1">
            {menuItems.map((item) => (
              <Link key={item.id} href={item.href} passHref>
                <Button
                  variant="ghost"
                  className={cn(
                    'w-full cursor-pointer',
                    !isSidebarCollapsed && 'hover:shadow-md', // Apply shadow only when not collapsed
                    isSidebarCollapsed ? 'justify-center px-0' : 'justify-start px-4'
                  )}
                  title={isSidebarCollapsed ? item.label : ''} // Show tooltip only when collapsed
                >
                  <span className={cn('h-8 w-8 flex items-center justify-center border border-gray-200 rounded-md', isSidebarCollapsed ? '' : 'mr-3')}>
                    <item.icon className={cn('h-5 w-5 text-blue-500', isSidebarCollapsed && 'hover:text-blue-800')} /> {/* Darken icon on hover when collapsed */}
                  </span>
                  <span
                    className={cn(
                      'transition-opacity duration-300',
                      !isSidebarCollapsed && 'hover:text-blue-800', // Apply text hover only when not collapsed
                      isSidebarCollapsed ? 'opacity-0 hidden' : 'opacity-100'
                    )}
                  >
                    {item.label}
                  </span>
                </Button>
              </Link>
            ))}
          </nav>
        </ScrollArea>
      </aside>
    </>
  );
};

export default Sidebar;