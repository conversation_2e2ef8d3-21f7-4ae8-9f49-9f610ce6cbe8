import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children, requireOnboarding = false }) => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // User is not authenticated, redirect to login
        router.push('/login');
      } else if (requireOnboarding && !user.isOnboarded) {
        // User is authenticated but not onboarded, redirect to onboarding
        router.push('/onboarding');
      } else if (!requireOnboarding && user && !user.isOnboarded) {
        // User is authenticated but not onboarded, and we're not on onboarding page
        if (router.pathname !== '/onboarding') {
          router.push('/onboarding');
        }
      }
    }
  }, [user, loading, router, requireOnboarding]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render children if user is not authenticated
  if (!user) {
    return null;
  }

  // Don't render children if onboarding is required but not completed
  if (requireOnboarding && !user.isOnboarded) {
    return null;
  }

  return children;
};

export default ProtectedRoute;
