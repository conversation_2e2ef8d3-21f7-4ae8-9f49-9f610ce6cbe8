{"name": "sales-dashboard-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.522.0", "mongodb": "^6.17.0", "next": "15.3.4", "next-auth": "^4.24.11", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4"}}