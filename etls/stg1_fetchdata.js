import fetch from 'node-fetch';

async function main() {

const host = {
    serverName: 'db2.tatilsepeti.com',
    port: 1433,
    databaseName: 'master',
    URL: '********************************************************************',
    username: 'taner.subasi',
    password: 'GRT-83bk+8'
};
    const q_tesis_listesi = `
                        select * from (
                            SELECT  
                                T.TESIS_ID,
                                T.KATEGORI,
                                T.TESIS_ADI,
                                T.ZINCIR_ADI,
                                T.ZINCIR_ADI_2,
                                T.TESIS_YAPIM_YILI,
                                T.TESIS_YENILEME_YILI,
                                T.TESIS_KAYIT_TARIHI,
                                T.BOLGE_ADI,
                                T.ALT_BOLGE_ADI,
                                T.BOLGE_DETAY,
                                T.LATITUDE,
                                T.LONGITUDE,
                                T.TESIS_ONAY,
                                T.DURUM,
                                CASE 
                                    WHEN T.TESIS_ONAY = 'Onaylandı' AND T.DURUM IN ('Sorunuz...', '<PERSON><PERSON>') THEN 'TRUE'
                                    ELSE 'FALSE'
                                END AS WEBSITE_LISTED,
                                T.KIMLER_KALMALI,
                                T.OTOMATIK_FIYATLANDIRMA,
                                T.URL,
                                T.SEO_URL,
                                T.TESIS_KAPASITE,
                                T.KONAKLAMA_ACIKLAMA,
                                T.ACIKLAMA,
                                T.ERKEK_KABUL_EDILMEZ,
                                T.UYELIK_KATEGORI,
                                D.TESIS_DETAY_ID,
                                D.TESIS_UNVAN,
                                D.ADRES1 AS ADRES1_DETAY,
                                D.ADRES2,
                                D.SEMT,
                                D.SEHIR,
                                D.TEL,
                                D.KONTAK_AD,
                                D.KONTAK_SOYAD,
                                D.KONTAK_GOREV,
                                D.KONTAK_EMAIL,
                                D.KONTAK_TEL,
                                D.KONTAK_DAHILI,
                                D.PERSONEL_ACIKLAMA_ARTI,
                                D.PERSONEL_ACIKLAMA_EKSI,
                                D.PERSONEL_ACIKLAMA_NOT,
                                D.ACIK_HAVUZ_BOYUTU,
                                D.ACIK_HAVUZ_DERINLIGI,
                                D.KAPALI_HAVUZ_BOYUTU,
                                D.KAPALI_HAVUZ_DERINLIGI,
                                D.DENIZ_OZELLIKLERI,
                                D.KOMISYON,
                                D.FIYATLANDIRMA_ACIKLAMA,
                                K.ONCELIK AS T_ONCELIK,
                                D.SOZLESME_ONAY,
                                D.CALISMA_MODELI,
                                D.ALINAN_ONLEMLER,
                                D.OTEL_ISLETME_BELGE_TIPI,
                                D.OTEL_ISLETME_NUMARASI,
                                K.TARIH,
                                T.TESIS_PUANI,
                                K.TESIS_PUANI AS GUNLUK_TESIS_PUANI,
                                K.TP_TESIS_MIMARISI,
                                K.TP_TEMIZLIK_HIJYEN,
                                K.TP_YIYECEK_ICECEK,
                                K.TP_ANIMASYON,
                                K.TP_SERVIS_PERSONEL, 
                                K.BRUT_CIRO,
                                K.BRUT_REZERVASYON_SAYISI,
                                K.NET_CIRO,
                                K.NET_REZERVASYON_SAYISI,
                                K.IPTAL_SAYISI,
                                T.BOLGE_NO,
                                T.ALT_BOLGE_NO,
                                K.FTGD_ID
                            FROM 
                                TSBI.dbo.DIM_TESIS T
                            LEFT JOIN 
                                TSBI.dbo.DIM_TESIS_DETAY D ON T.TESIS_ID = D.TESIS_ID
                            OUTER APPLY (
                                SELECT TOP 1 *
                                FROM TSBI.dbo.FACT_TESIS_GUNLUK_DETAY KK
                                WHERE KK.TESIS_ID = T.TESIS_ID
                                ORDER BY KK.TARIH DESC
                            ) K
                        --where D.TESIS_ID IN (4334)
                        ) a

    `

};

main();

// --tesis listesi

    