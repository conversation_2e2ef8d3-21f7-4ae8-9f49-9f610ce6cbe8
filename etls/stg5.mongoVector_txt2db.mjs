import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { ObjectId } from 'mongodb';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
dotenv.config();
import { GoogleGenerativeAI } from '@google/generative-ai';
// import { pipeline, env } from '@xenova/transformers';
// .env dosyasını yükle

// WebAssembly için wasm yollarını ayarlayalım
// env.backends.onnx.wasm.wasmPaths = 'https://onnxruntime.github.io/onnx-js-demo/dist/ ';

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            {
                mongoCollection: 'tourai.data.dim.hotelsdomestic',
                needsVectorEmbedding: true,
                embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            },
        ];

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');

        // Embedding dosyası ayarları
        const embeddingDir = path.resolve(__dirname || path.dirname(new URL(import.meta.url).pathname), './');
        const embeddingFile = path.join(embeddingDir, 'stg4.embeddings.txt');

        // Embedding dosyasını satır satır oku ve MongoDB'ye yaz
        const embeddingLines = (await fs.readFile(embeddingFile, 'utf-8')).split('\n').filter(Boolean);

        const collection = mongoDb.collection(tables2Sync[0].mongoCollection);
        let updatedCount = 0;
        let failedCount = 0;
        const startTime = Date.now();

        for (let i = 0; i < embeddingLines.length; i++) {
            const line = embeddingLines[i];
            try {
                const obj = JSON.parse(line);
                if (obj._id && obj.embedding) {
                    await collection.updateOne(
                        { _id: new ObjectId(obj._id) },
                        { $set: { vectorEmbedding: obj.embedding } }
                    );
                    updatedCount++;
                }
            } catch (err) {
                failedCount++;
                console.error('Satır JSON parse hatası:', err, line);
            }

            if ((i + 1) % 1000 === 0) {
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                console.log(`${i + 1} kayıt işlendi. Geçen süre: ${elapsed} sn`);
            }
        }

        const totalElapsed = ((Date.now() - startTime) / 1000).toFixed(1);
        console.log(`\nTüm kayıtlar tamamlandı. Toplam süre: ${totalElapsed} sn`);
        console.log(`Başarılı kayıt: ${updatedCount}`);
        console.log(`Başarısız kayıt: ${failedCount}`);

        // Bağlantıyı kapat
        await mongoClient.close();
        // process.exit(1);
        // await mongoClient.close();
    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// __dirname'i ES module ortamında tanımla
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Script'i çalıştır
main();
