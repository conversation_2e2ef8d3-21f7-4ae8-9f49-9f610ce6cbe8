import sql from 'mssql';
import fs from 'fs';
import path from 'path';

async function main() {
    try {
        // SQL Server bağlantı konfigürasyonu
        const dtBop = Date.now();
        const config = {
            server: 'db2.tatilsepeti.com',
            port: 1433,
            database: 'master',
            user: 'taner.subasi',
            password: 'GRT-83bk+8',
            options: {
                encrypt: false, // Azure için true, on-premise için false
                trustServerCertificate: true,
                enableArithAbort: true,
                requestTimeout: 300000, // 5 dakika timeout
                connectionTimeout: 30000 // 30 saniye connection timeout
            }
        };
        // SQL sorgusu
        const q_tesis_listesi = `
            select * from (
                SELECT
                    T.TESIS_ID,
                    T.KATEGORI,
                    T.TESIS_ADI,
                    T.ZINCIR_ADI,
                    T.ZINCIR_ADI_2,
                    T.TESIS_YAPIM_YILI,
                    T.TESIS_YENILEME_YILI,
                    T.TESIS_KAYIT_TARIHI,
                    <PERSON><PERSON>_<PERSON>,
                    T.ALT_B<PERSON>GE_ADI,
                    <PERSON><PERSON>_<PERSON>,
                    T.<PERSON>,
                    T.LONGI<PERSON>DE,
                    T.T<PERSON>IS_ONAY,
                    T.DURUM,
                    CASE
                        WHEN T.TESIS_ONAY = 'Onaylandı' AND T.DURUM IN ('Sorunuz...', 'Tesis Kapalı') THEN 'TRUE'
                        ELSE 'FALSE'
                    END AS WEBSITE_LISTED,
                    T.KIMLER_KALMALI,
                    T.OTOMATIK_FIYATLANDIRMA,
                    T.URL,
                    T.SEO_URL,
                    T.TESIS_KAPASITE,
                    T.KONAKLAMA_ACIKLAMA,
                    T.ACIKLAMA,
                    T.ERKEK_KABUL_EDILMEZ,
                    T.UYELIK_KATEGORI,
                    D.TESIS_DETAY_ID,
                    D.TESIS_UNVAN,
                    D.ADRES1 AS ADRES1_DETAY,
                    D.ADRES2,
                    D.SEMT,
                    D.SEHIR,
                    D.TEL,
                    D.KONTAK_AD,
                    D.KONTAK_SOYAD,
                    D.KONTAK_GOREV,
                    D.KONTAK_EMAIL,
                    D.KONTAK_TEL,
                    D.KONTAK_DAHILI,
                    D.PERSONEL_ACIKLAMA_ARTI,
                    D.PERSONEL_ACIKLAMA_EKSI,
                    D.PERSONEL_ACIKLAMA_NOT,
                    D.ACIK_HAVUZ_BOYUTU,
                    D.ACIK_HAVUZ_DERINLIGI,
                    D.KAPALI_HAVUZ_BOYUTU,
                    D.KAPALI_HAVUZ_DERINLIGI,
                    D.DENIZ_OZELLIKLERI,
                    D.KOMISYON,
                    D.FIYATLANDIRMA_ACIKLAMA,
                    K.ONCELIK AS T_ONCELIK,
                    D.SOZLESME_ONAY,
                    D.CALISMA_MODELI,
                    D.ALINAN_ONLEMLER,
                    D.OTEL_ISLETME_BELGE_TIPI,
                    D.OTEL_ISLETME_NUMARASI,
                    K.TARIH,
                    T.TESIS_PUANI,
                    K.TESIS_PUANI AS GUNLUK_TESIS_PUANI,
                    K.TP_TESIS_MIMARISI,
                    K.TP_TEMIZLIK_HIJYEN,
                    K.TP_YIYECEK_ICECEK,
                    K.TP_ANIMASYON,
                    K.TP_SERVIS_PERSONEL,
                    K.BRUT_CIRO,
                    K.BRUT_REZERVASYON_SAYISI,
                    K.NET_CIRO,
                    K.NET_REZERVASYON_SAYISI,
                    K.IPTAL_SAYISI,
                    T.BOLGE_NO,
                    T.ALT_BOLGE_NO,
                    K.FTGD_ID
                FROM
                    TSBI.dbo.DIM_TESIS T
                LEFT JOIN
                    TSBI.dbo.DIM_TESIS_DETAY D ON T.TESIS_ID = D.TESIS_ID
                OUTER APPLY (
                    SELECT TOP 1 *
                    FROM TSBI.dbo.FACT_TESIS_GUNLUK_DETAY KK
                    WHERE KK.TESIS_ID = T.TESIS_ID
                    ORDER BY KK.TARIH DESC
                ) K
                where D.TESIS_ID IN (4334)
            ) a
        `;
        const q_ga4_stats = `
                SELECT 
                    FORMAT(G.DATE, 'yyyy-MM') AS TARIH,
                    TRY_CAST(G.URUN_ID AS INT) as TESIS_ID,
                    T.TESIS_ADI,
                    T.BOLGE_ADI,
                    T.KATEGORI,
                    SUM(G.ITEMS_VIEWED) AS TOTAL_ITEMS_VIEWED,
                    ROUND(SUM(G.ITEM_REVENUE), 0) AS TOTAL_ITEM_REVENUE,
                    SUM(G.TOTAL_USERS) AS TOTAL_USERS_SUM
                FROM 
                    TSBI.dbo.RAW_GA4_DATA_ITEMS G
                INNER JOIN 
                    TSBI.dbo.DIM_TESIS T
                    --ON G.URUN_ID = T.TESIS_ID
                    ON TRY_CAST(G.URUN_ID AS INT) = T.TESIS_ID
                WHERE 
                    G.[DATE] >= DATEFROMPARTS(YEAR(GETDATE()) - 1, 1, 1) 
                GROUP BY 
                    FORMAT(G.DATE, 'yyyy-MM'), G.URUN_ID, T.TESIS_ADI, T.BOLGE_ADI, T.KATEGORI
                ORDER BY 
                    G.URUN_ID, FORMAT(G.DATE, 'yyyy-MM') DESC;
        `;
        const q_salesPerItems = `
                select FORMAT(SATIS_TARIHI, 'yyyy-MM') AS SATIS_DONEM
                    , URUN_ID as TESIS_ID
                    , BAYI_ID, BAYI_ADI
                    , ACENTA_ID, a.ACENTA_ADI
                    , a.REZERVASYON_TIPI
                    , a.SATIS_KANALI 
                    , a.TESIS_ODA_TIP 
                    , a.PANSIYON_TIPI
                    , a.URUN_GRUBU
                    , count(*) REZV, SUM(a.REZERVASYON_GUN_SAYISI) REZV_GUNSAYISI
                    , count(a.YETISKIN_SAYISI) nYETISKIN, count(a.COCUK_SAYISI) nCocuk
                    , ROUND(sum(TOPLAM_TUTAR_TL), 0) tTutar, ROUND(sum(ODENEN_TUTAR), 0) tOdenen, ROUND(sum(TESIS_MALIYETI), 0) tMaliyet
                    from (
                SELECT *, 
                --case when BOLGE_ADI = 'Kıbrıs' then 'Kıbrıs' else 'Yurtiçi' end as SatisBolgeAdi , --- UURUN_GRUBU
                FORMAT(KAYIT_TARIHI, 'yyyy-MM-dd') AS KAYIT_TARIHI_D,
                FORMAT(SATIS_TARIHI, 'yyyy-MM-dd') AS SATIS_TARIHI_D
                FROM TSBI.dbo.RAW_REZERVASYON_SATIS_NET
                WHERE 1=1
                AND BAYI_ID not in(65)
                --AND URUN_ID = 4334
                AND URUN_TIPI = 'Tesis'
                --AND SATIS_TARIHI >= '2025-01-01'
                AND SATIS_TARIHI >= DATEFROMPARTS(YEAR(GETDATE()) - 1, 1, 1) 
                and REZERVASYON_DURUM_NO not in(90, 98, 2, 110, 130, 133, 134, 123, 120, 105, 116)
                ) a
                group by FORMAT(SATIS_TARIHI, 'yyyy-MM')
                    , URUN_ID
                    , BAYI_ID, BAYI_ADI
                    , ACENTA_ID, a.ACENTA_ADI
                    , REZERVASYON_TIPI
                    , SATIS_KANALI 
                    , a.TESIS_ODA_TIP 
                    , a.PANSIYON_TIPI
                    , a.URUN_GRUBU
                order by URUN_ID, SATIS_DONEM 
        `;

        const queries2Execute = [
            { name: 'q_tesis_listesi', query: q_tesis_listesi, fileName: 'data_tesis_listesi.json' },
            { name: 'q_ga4_stats', query: q_ga4_stats, fileName: 'data_ga4_stats.json' },
            { name: 'q_salesPerItems', query: q_salesPerItems, fileName: 'data_salesPerItems.json' },
        ];
        console.log('SQL Server\'a bağlanılıyor...');

        // SQL Server'a bağlan
        const pool = await sql.connect(config);
        console.log('Bağlantı başarılı!');

        console.log('Sorgu çalıştırılıyor...');

        // Sorguyu çalıştır
        const result = await pool.request().query(q_tesis_listesi);

        console.log(`Sorgu tamamlandı. ${result.recordset.length} kayıt bulundu.`);

        // Sonuçları formatla
        const output = {
            timestamp: new Date().toISOString(),
            query: 'q_tesis_listesi',
            recordCount: result.recordset.length,
            data: result.recordset,
            elapsedTime: Date.now() - dtBop,
        };

        // JSON formatında results.txt dosyasına kaydet
        const outputPath = path.join(process.cwd(), 'results.txt');
        const jsonOutput = JSON.stringify(output, null, 2);

        fs.writeFileSync(outputPath, jsonOutput, 'utf8');

        console.log(`Sonuçlar ${outputPath} dosyasına kaydedildi.`);
        console.log('İşlem tamamlandı!');

        // Bağlantıyı kapat
        await pool.close();

    } catch (error) {
        console.error('Hata oluştu:', error);

        // Hata bilgilerini de dosyaya kaydet
        const errorOutput = {
            timestamp: new Date().toISOString(),
            query: 'q_tesis_listesi',
            error: true,
            errorMessage: error.message,
            errorStack: error.stack
        };

        const outputPath = path.join(process.cwd(), 'results.txt');
        fs.writeFileSync(outputPath, JSON.stringify(errorOutput, null, 2), 'utf8');

        process.exit(1);
    }
}

// Script'i çalıştır
main();