import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();

    try {
        const files2Import = [
            { name: 'q_tesis_listesi', fileName: 'data_tesis_listesi.json' },
            { name: 'q_ga4_stats', fileName: 'data_ga4_stats.json' },
            { name: 'q_salesPerItems', fileName: 'data_salesPerItems.json' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanını aç/oluştur
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });

        console.log(`✓ SQLite veritabanı oluşturuldu: ${dbPath}`);
        console.log(`Toplam ${files2Import.length} dosya işlenecek...`);

        // Her dosyayı sıras<PERSON>yla <PERSON>
        for (let i = 0; i < files2Import.length; i++) {
            const fileInfo = files2Import[i];
            const fileStartTime = Date.now();

            console.log(`\n[${i + 1}/${files2Import.length}] ${fileInfo.fileName} dosyası işleniyor...`);

            try {
                // JSON dosyasını oku
                const filePath = path.join(process.cwd(), fileInfo.fileName);

                if (!fs.existsSync(filePath)) {
                    console.log(`⚠️  ${fileInfo.fileName} dosyası bulunamadı, atlanıyor...`);
                    continue;
                }

                const fileContent = fs.readFileSync(filePath, 'utf8');
                const jsonData = JSON.parse(fileContent);

                if (!jsonData.data || !Array.isArray(jsonData.data)) {
                    console.log(`⚠️  ${fileInfo.fileName} dosyasında geçerli data bulunamadı, atlanıyor...`);
                    continue;
                }

                const records = jsonData.data;
                console.log(`  📄 ${records.length} kayıt bulundu`);

                if (records.length === 0) {
                    console.log(`  ⚠️  Kayıt bulunamadı, tablo oluşturulmayacak`);
                    continue;
                }

                // Tabloyu drop et
                console.log(`  🗑️  ${fileInfo.name} tablosu drop ediliyor...`);
                await db.exec(`DROP TABLE IF EXISTS ${fileInfo.name}`);

                // İlk kaydın kolonlarını al ve tablo oluştur
                const firstRecord = records[0];
                const columns = Object.keys(firstRecord);

                // Kolon tiplerini belirle
                const columnDefinitions = columns.map(col => {
                    const value = firstRecord[col];
                    let type = 'TEXT';

                    if (typeof value === 'number') {
                        type = Number.isInteger(value) ? 'INTEGER' : 'REAL';
                    } else if (typeof value === 'boolean') {
                        type = 'INTEGER'; // SQLite'da boolean için
                    } else if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
                        type = 'TEXT'; // Tarih için TEXT kullan
                    }

                    return `"${col}" ${type}`;
                }).join(', ');

                // Tabloyu oluştur
                const createTableSQL = `CREATE TABLE "${fileInfo.name}" (${columnDefinitions})`;
                console.log(`  🏗️  ${fileInfo.name} tablosu oluşturuluyor...`);
                await db.exec(createTableSQL);

                // Verileri ekle
                console.log(`  📝 Veriler ${fileInfo.name} tablosuna yazılıyor...`);

                const placeholders = columns.map(() => '?').join(', ');
                const insertSQL = `INSERT INTO "${fileInfo.name}" (${columns.map(col => `"${col}"`).join(', ')}) VALUES (${placeholders})`;

                const stmt = await db.prepare(insertSQL);

                let insertedCount = 0;
                for (const record of records) {
                    const values = columns.map(col => {
                        let value = record[col];

                        // Boolean değerleri 0/1'e çevir
                        if (typeof value === 'boolean') {
                            value = value ? 1 : 0;
                        }
                        // null/undefined değerleri null yap
                        else if (value === undefined) {
                            value = null;
                        }

                        return value;
                    });

                    await stmt.run(values);
                    insertedCount++;
                }

                await stmt.finalize();

                const fileElapsedTime = Date.now() - fileStartTime;
                console.log(`  ✅ ${insertedCount} kayıt başarıyla eklendi. (${fileElapsedTime}ms)`);

            } catch (fileError) {
                console.error(`  ❌ ${fileInfo.fileName} işlenirken hata:`, fileError.message);
            }
        }

        const totalElapsedTime = Date.now() - dtBop;
        console.log(`\n🎉 Tüm işlemler tamamlandı! Toplam süre: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        // Veritabanını kapat
        await db.close();

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();


