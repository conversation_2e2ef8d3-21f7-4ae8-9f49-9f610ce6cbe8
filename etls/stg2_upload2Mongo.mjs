import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();

    try {
        const files2Import = [
            { name: 'q_tesis_listesi', fileName: 'data_tesis_listesi.json' },
            { name: 'q_ga4_stats', fileName: 'data_ga4_stats.json' },
            { name: 'q_salesPerItems', fileName: 'data_salesPerItems.json' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });

        // Eğer veritabanı mevcutsa, hedef tabloları önceden drop et
        if (dbExists) {
            console.log('🗑️  Mevcut hedef tablolar drop ediliyor...');
            for (const fileInfo of files2Import) {
                try {
                    await db.exec(`DROP TABLE IF EXISTS "${fileInfo.name}"`);
                    console.log(`  ✓ ${fileInfo.name} tablosu drop edildi`);
                } catch (dropError) {
                    console.log(`  ⚠️  ${fileInfo.name} tablosu drop edilemedi: ${dropError.message}`);
                }
            }
        }

        console.log(`✓ SQLite veritabanı oluşturuldu: ${dbPath}`);
        console.log(`Toplam ${files2Import.length} dosya işlenecek...`);

        // Her dosyayı sırasıyla işle
        for (let i = 0; i < files2Import.length; i++) {
            const fileInfo = files2Import[i];
            const fileStartTime = Date.now();

            console.log(`\n[${i + 1}/${files2Import.length}] ${fileInfo.fileName} dosyası işleniyor...`);

            try {
                // JSON dosyasını oku
                const filePath = path.join(process.cwd(), fileInfo.fileName);

                if (!fs.existsSync(filePath)) {
                    console.log(`⚠️  ${fileInfo.fileName} dosyası bulunamadı, atlanıyor...`);
                    continue;
                }

                const fileContent = fs.readFileSync(filePath, 'utf8');
                const jsonData = JSON.parse(fileContent);

                if (!jsonData.data || !Array.isArray(jsonData.data)) {
                    console.log(`⚠️  ${fileInfo.fileName} dosyasında geçerli data bulunamadı, atlanıyor...`);
                    continue;
                }

                const records = jsonData.data;
                console.log(`  📄 ${records.length} kayıt bulundu`);

                if (records.length === 0) {
                    console.log(`  ⚠️  Kayıt bulunamadı, tablo oluşturulmayacak`);
                    continue;
                }

                // İlk kaydın kolonlarını al ve tablo oluştur
                const firstRecord = records[0];
                const columns = Object.keys(firstRecord);

                // Kolon tiplerini belirle
                const columnDefinitions = columns.map(col => {
                    const value = firstRecord[col];
                    let type = 'TEXT';

                    if (typeof value === 'number') {
                        type = Number.isInteger(value) ? 'INTEGER' : 'REAL';
                    } else if (typeof value === 'boolean') {
                        type = 'INTEGER'; // SQLite'da boolean için
                    } else if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
                        type = 'TEXT'; // Tarih için TEXT kullan
                    }

                    return `"${col}" ${type}`;
                }).join(', ');

                // Tabloyu oluştur
                const createTableSQL = `CREATE TABLE "${fileInfo.name}" (${columnDefinitions})`;
                console.log(`  🏗️  ${fileInfo.name} tablosu oluşturuluyor...`);
                await db.exec(createTableSQL);

                // Verileri ekle
                console.log(`  📝 Veriler ${fileInfo.name} tablosuna yazılıyor...`);

                const placeholders = columns.map(() => '?').join(', ');
                const insertSQL = `INSERT INTO "${fileInfo.name}" (${columns.map(col => `"${col}"`).join(', ')}) VALUES (${placeholders})`;

                const stmt = await db.prepare(insertSQL);

                let insertedCount = 0;
                for (const record of records) {
                    const values = columns.map(col => {
                        let value = record[col];

                        // Boolean değerleri 0/1'e çevir
                        if (typeof value === 'boolean') {
                            value = value ? 1 : 0;
                        }
                        // null/undefined değerleri null yap
                        else if (value === undefined) {
                            value = null;
                        }

                        return value;
                    });

                    await stmt.run(values);
                    insertedCount++;
                }

                await stmt.finalize();

                const fileElapsedTime = Date.now() - fileStartTime;
                console.log(`  ✅ ${insertedCount} kayıt başarıyla eklendi. (${fileElapsedTime}ms)`);

            } catch (fileError) {
                console.error(`  ❌ ${fileInfo.fileName} işlenirken hata:`, fileError.message);
            }
        }

        const totalElapsedTime = Date.now() - dtBop;
        console.log(`\n🎉 Tüm işlemler tamamlandı! Toplam süre: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        const q_summary = `
                SELECT 
                    T.TESIS_ID,
                    T.KATEGORI,
                    T.TESIS_ADI,
                    T.ZINCIR_ADI,
                    T.ZINCIR_ADI_2,
                    T.TESIS_YAPIM_YILI,
                    T.TESIS_YENILEME_YILI,
                    T.TESIS_KAYIT_TARIHI,
                    T.BOLGE_ADI,
                    T.ALT_BOLGE_ADI,
                    T.BOLGE_DETAY,
                    T.LATITUDE,
                    T.LONGITUDE,
                    T.TESIS_ONAY,
                    T.DURUM,
                    T.WEBSITE_LISTED,
                    T.KIMLER_KALMALI,
                    T.OTOMATIK_FIYATLANDIRMA,
                    T.URL,
                    T.SEO_URL,
                    T.TESIS_KAPASITE,
                    T.KONAKLAMA_ACIKLAMA,
                    T.ACIKLAMA,
                    T.ERKEK_KABUL_EDILMEZ,
                    T.UYELIK_KATEGORI,
                    T.TESIS_DETAY_ID,
                    T.TESIS_UNVAN,
                    T.ADRES1_DETAY,
                    T.ADRES2,
                    T.SEMT,
                    T.SEHIR,
                    T.TEL,
                    T.KONTAK_AD,
                    T.KONTAK_SOYAD,
                    T.KONTAK_GOREV,
                    T.KONTAK_EMAIL,
                    T.KONTAK_TEL,
                    T.KONTAK_DAHILI,
                    T.PERSONEL_ACIKLAMA_ARTI,
                    T.PERSONEL_ACIKLAMA_EKSI,
                    T.PERSONEL_ACIKLAMA_NOT,
                    T.ACIK_HAVUZ_BOYUTU,
                    T.ACIK_HAVUZ_DERINLIGI,
                    T.KAPALI_HAVUZ_BOYUTU,
                    T.KAPALI_HAVUZ_DERINLIGI,
                    T.DENIZ_OZELLIKLERI,
                    T.KOMISYON,
                    T.FIYATLANDIRMA_ACIKLAMA,
                    T.T_ONCELIK,
                    T.SOZLESME_ONAY,
                    T.CALISMA_MODELI,
                    T.ALINAN_ONLEMLER,
                    T.OTEL_ISLETME_BELGE_TIPI,
                    T.OTEL_ISLETME_NUMARASI,
                    T.TARIH,
                    T.TESIS_PUANI,
                    T.GUNLUK_TESIS_PUANI,
                    T.TP_TESIS_MIMARISI,
                    T.TP_TEMIZLIK_HIJYEN,
                    T.TP_YIYECEK_ICECEK,
                    T.TP_ANIMASYON,
                    T.TP_SERVIS_PERSONEL,
                    T.BRUT_CIRO,
                    T.BRUT_REZERVASYON_SAYISI,
                    T.NET_CIRO,
                    T.NET_REZERVASYON_SAYISI,
                    T.IPTAL_SAYISI,
                    T.BOLGE_NO,
                    T.ALT_BOLGE_NO,
                    T.FTGD_ID,
                    (
                        SELECT '[' || GROUP_CONCAT(
                            '{' ||
                                '"Satis_Donem":"' || SATIS_DONEM || '",' ||
                                '"BAYI_ID":' || BAYI_ID || ',' ||
                                '"BAYI_ADI":"' || BAYI_ADI || '",' ||
                                '"ACENTA_ID":' || ACENTA_ID || ',' ||
                                '"ACENTA_ADI":"' || ACENTA_ADI || '",' ||
                                '"REZERVASYON_TIPI":"' || REZERVASYON_TIPI || '",' ||
                                '"Satis_Kanalı":"' || SATIS_KANALI || '",' ||
                                '"Oda_Tipi":"' || TESIS_ODA_TIP || '",' ||
                                '"Pansiyon_Tipi":"' || PANSIYON_TIPI || '",' ||
                                '"Rezervasyon_Sayısı":' || REZV || ',' ||
                                '"Gun_Sayısı":' || REZV_GUNSAYISI || ',' ||
                                '"Yetiskin_Sayısı":' || nYETISKIN || ',' ||
                                '"Cocuk_Sayısı":' || nCocuk || ',' ||
                                '"Toplam_Tutar":' || tTutar || ',' ||
                                '"Odenen_Tutar":' || tOdenen || ',' ||
                                '"Maliyet":' || tMaliyet ||
                            '}'
                        , ',') || ']'
                        FROM FACT_SALES_NET
                        WHERE TESIS_ID = T.TESIS_ID
                    ) AS SORGU_EK1_JSON,
                    (
                        SELECT '[' || GROUP_CONCAT(
                            '{' ||
                                '"Tarih":"' || TARIH || '",' ||
                                '"Toplam_Item_Izlenme":' || TOTAL_ITEMS_VIEWED || ',' ||
                                '"Toplam_Item_Gelir":' || TOTAL_ITEM_REVENUE || ',' ||
                                '"Toplam_Kullanici":' || TOTAL_USERS_SUM ||
                            '}'
                        , ',') || ']'
                        FROM FACT_GA4_DATA
                        WHERE TESIS_ID = T.TESIS_ID
                    ) AS SORGU_EK2_JSON 
                FROM DIM_TESIS_ADV AS T
                --where TESIS_ID IN(4334);
        `;

        // Veritabanını kapat
        await db.close();

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();


