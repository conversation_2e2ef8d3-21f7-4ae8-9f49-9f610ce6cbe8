import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();

    try {
        const files2Import = [
            { name: 'q_tesis_listesi', fileName: 'data_tesis_listesi.json' },
            { name: 'q_ga4_stats', fileName: 'data_ga4_stats.json' },
            { name: 'q_salesPerItems', fileName: 'data_salesPerItems.json' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        const q_summary = `
                SELECT 
                    T.TESIS_ID,
                    T.KATEGORI,
                    T.TESIS_ADI,
                    T.ZINCIR_ADI,
                    T.ZINCIR_ADI_2,
                    T.TESIS_YAPIM_YILI,
                    T.TESIS_YENILEME_YILI,
                    T.TESIS_KAYIT_TARIHI,
                    T.BOLGE_ADI,
                    T.ALT_BOLGE_ADI,
                    T.BOLGE_DETAY,
                    T.LATITUDE,
                    T.LONGITUDE,
                    T.TESIS_ONAY,
                    T.DURUM,
                    T.WEBSITE_LISTED,
                    T.KIMLER_KALMALI,
                    T.OTOMATIK_FIYATLANDIRMA,
                    T.URL,
                    T.SEO_URL,
                    T.TESIS_KAPASITE,
                    T.KONAKLAMA_ACIKLAMA,
                    T.ACIKLAMA,
                    T.ERKEK_KABUL_EDILMEZ,
                    T.UYELIK_KATEGORI,
                    T.TESIS_DETAY_ID,
                    T.TESIS_UNVAN,
                    T.ADRES1_DETAY,
                    T.ADRES2,
                    T.SEMT,
                    T.SEHIR,
                    T.TEL,
                    T.KONTAK_AD,
                    T.KONTAK_SOYAD,
                    T.KONTAK_GOREV,
                    T.KONTAK_EMAIL,
                    T.KONTAK_TEL,
                    T.KONTAK_DAHILI,
                    T.PERSONEL_ACIKLAMA_ARTI,
                    T.PERSONEL_ACIKLAMA_EKSI,
                    T.PERSONEL_ACIKLAMA_NOT,
                    T.ACIK_HAVUZ_BOYUTU,
                    T.ACIK_HAVUZ_DERINLIGI,
                    T.KAPALI_HAVUZ_BOYUTU,
                    T.KAPALI_HAVUZ_DERINLIGI,
                    T.DENIZ_OZELLIKLERI,
                    T.KOMISYON,
                    T.FIYATLANDIRMA_ACIKLAMA,
                    T.T_ONCELIK,
                    T.SOZLESME_ONAY,
                    T.CALISMA_MODELI,
                    T.ALINAN_ONLEMLER,
                    T.OTEL_ISLETME_BELGE_TIPI,
                    T.OTEL_ISLETME_NUMARASI,
                    T.TARIH,
                    T.TESIS_PUANI,
                    T.GUNLUK_TESIS_PUANI,
                    T.TP_TESIS_MIMARISI,
                    T.TP_TEMIZLIK_HIJYEN,
                    T.TP_YIYECEK_ICECEK,
                    T.TP_ANIMASYON,
                    T.TP_SERVIS_PERSONEL,
                    T.BRUT_CIRO,
                    T.BRUT_REZERVASYON_SAYISI,
                    T.NET_CIRO,
                    T.NET_REZERVASYON_SAYISI,
                    T.IPTAL_SAYISI,
                    T.BOLGE_NO,
                    T.ALT_BOLGE_NO,
                    T.FTGD_ID,
                    (
                        SELECT '[' || GROUP_CONCAT(
                            '{' ||
                                '"Satis_Donem":"' || COALESCE(SATIS_DONEM, '') || '",' ||
                                '"BAYI_ID":' || COALESCE(BAYI_ID, 0) || ',' ||
                                '"BAYI_ADI":"' || COALESCE(BAYI_ADI, '') || '",' ||
                                '"ACENTA_ID":' || COALESCE(ACENTA_ID, 0) || ',' ||
                                '"ACENTA_ADI":"' || COALESCE(ACENTA_ADI, '') || '",' ||
                                '"REZERVASYON_TIPI":"' || COALESCE(REZERVASYON_TIPI, '') || '",' ||
                                '"Satis_Kanalı":"' || COALESCE(SATIS_KANALI, '') || '",' ||
                                '"Oda_Tipi":"' || COALESCE(TESIS_ODA_TIP, '') || '",' ||
                                '"Pansiyon_Tipi":"' || COALESCE(PANSIYON_TIPI, '') || '",' ||
                                '"Rezervasyon_Sayısı":' || COALESCE(REZV, 0) || ',' ||
                                '"Gun_Sayısı":' || COALESCE(REZV_GUNSAYISI, 0) || ',' ||
                                '"Yetiskin_Sayısı":' || COALESCE(nYETISKIN, 0) || ',' ||
                                '"Cocuk_Sayısı":' || COALESCE(nCocuk, 0) || ',' ||
                                '"Toplam_Tutar":' || COALESCE(tTutar, 0) || ',' ||
                                '"Odenen_Tutar":' || COALESCE(tOdenen, 0) || ',' ||
                                '"Maliyet":' || COALESCE(tMaliyet, 0) ||
                            '}'
                        , ',') || ']'
                        FROM q_salesPerItems
                        WHERE TESIS_ID = T.TESIS_ID
                    ) AS SORGU_EK1_JSON,
                    (
                        SELECT '[' || GROUP_CONCAT(
                            '{' ||
                                '"Tarih":"' || COALESCE(TARIH, '') || '",' ||
                                '"Toplam_Item_Izlenme":' || COALESCE(TOTAL_ITEMS_VIEWED, 0) || ',' ||
                                '"Toplam_Item_Gelir":' || COALESCE(TOTAL_ITEM_REVENUE, 0) || ',' ||
                                '"Toplam_Kullanici":' || COALESCE(TOTAL_USERS_SUM, 0) ||
                            '}'
                        , ',') || ']'
                        FROM q_ga4_stats
                        WHERE TESIS_ID = T.TESIS_ID
                    ) AS SORGU_EK2_JSON
                FROM q_tesis_listesi AS T
                --where TESIS_ID IN(4334);
        `;

        // q_summary sorgusunu çalıştır ve tabloyu oluştur
        console.log(`\n📊 q_summary sorgusu çalıştırılıyor...`);
        const summaryStartTime = Date.now();

        try {
            // q_summary tablosunu drop et
            await db.exec(`DROP TABLE IF EXISTS "q_summary"`);

            // q_summary sorgusunu çalıştır
            const summaryResult = await db.all(q_summary);

            if (summaryResult && summaryResult.length > 0) {
                console.log(`  ✓ q_summary sorgusu tamamlandı. ${summaryResult.length} kayıt bulundu.`);

                // İlk kaydın kolonlarını al ve q_summary tablosunu oluştur
                const firstRecord = summaryResult[0];
                const columns = Object.keys(firstRecord);

                // Kolon tiplerini belirle
                const columnDefinitions = columns.map(col => {
                    const value = firstRecord[col];
                    let type = 'TEXT';

                    if (typeof value === 'number') {
                        type = Number.isInteger(value) ? 'INTEGER' : 'REAL';
                    } else if (typeof value === 'boolean') {
                        type = 'INTEGER';
                    }

                    return `"${col}" ${type}`;
                }).join(', ');

                // q_summary tablosunu oluştur
                const createSummaryTableSQL = `CREATE TABLE "q_summary" (${columnDefinitions})`;
                await db.exec(createSummaryTableSQL);
                console.log(`  🏗️  q_summary tablosu oluşturuldu`);

                // Verileri q_summary tablosuna ekle
                const placeholders = columns.map(() => '?').join(', ');
                const insertSummarySQL = `INSERT INTO "q_summary" (${columns.map(col => `"${col}"`).join(', ')}) VALUES (${placeholders})`;

                const summaryStmt = await db.prepare(insertSummarySQL);

                let summaryInsertedCount = 0;
                for (const record of summaryResult) {
                    const values = columns.map(col => {
                        let value = record[col];

                        if (typeof value === 'boolean') {
                            value = value ? 1 : 0;
                        } else if (value === undefined) {
                            value = null;
                        }

                        return value;
                    });

                    await summaryStmt.run(values);
                    summaryInsertedCount++;
                }

                await summaryStmt.finalize();

                const summaryElapsedTime = Date.now() - summaryStartTime;
                console.log(`  ✅ q_summary tablosuna ${summaryInsertedCount} kayıt eklendi. (${summaryElapsedTime}ms)`);

            } else {
                console.log(`  ⚠️  q_summary sorgusu sonuç döndürmedi`);
            }

        } catch (summaryError) {
            console.error(`  ❌ q_summary sorgusu çalıştırılırken hata:`, summaryError.message);
        }

        const finalElapsedTime = Date.now() - dtBop;
        console.log(`\n🎉 Tüm işlemler tamamlandı! Toplam süre: ${finalElapsedTime}ms (${(finalElapsedTime/1000).toFixed(2)}s)`);
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        // Veritabanını kapat
        await db.close();

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();


